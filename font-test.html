<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Test</title>
    <style>
        @font-face {
            font-family: "Thunder";
            src: url("assets/fonts/Thunder-BoldLC.ttf") format("truetype");
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: "Thunder";
            src: url("assets/fonts/Thunder-SemiBoldLC.ttf") format("truetype");
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: "Thunder";
            src: url("assets/fonts/Thunder-LC.ttf") format("truetype");
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: "Thunder";
            src: url("assets/fonts/Thunder-MediumLC.ttf") format("truetype");
            font-weight: 500;
            font-style: normal;
            font-display: swap;
        }

        .test-thunder {
            font-family: "Thunder", Arial, sans-serif;
            font-size: 48px;
            font-weight: 700;
            color: #333;
            margin: 20px 0;
            border: 2px solid blue;
            padding: 10px;
        }

        .test-arial {
            font-family: Arial, sans-serif;
            font-size: 48px;
            font-weight: 700;
            color: #333;
            margin: 20px 0;
            border: 2px solid green;
            padding: 10px;
        }

        .font-info {
            font-family: Arial, sans-serif;
            font-size: 14px;
            color: #666;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Font Loading Test</h1>
    
    <div class="font-info">Thunder Font Test (should look different from Arial):</div>
    <div class="test-thunder">COMING SOON - Thunder Font</div>
    
    <div class="font-info">Arial Font Test (for comparison):</div>
    <div class="test-arial">COMING SOON - Arial Font</div>

    <script>
        // Check if fonts are loaded
        document.fonts.ready.then(function() {
            console.log('All fonts loaded');
            
            // Check if Thunder font is available
            if (document.fonts.check('48px Thunder')) {
                console.log('Thunder font is available');
            } else {
                console.log('Thunder font is NOT available');
            }
        });

        // Listen for font loading events
        document.fonts.addEventListener('loadingdone', function(event) {
            console.log('Font loading done:', event);
        });

        document.fonts.addEventListener('loadingerror', function(event) {
            console.log('Font loading error:', event);
        });
    </script>
</body>
</html>
