<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Grumpy Company - Coming Soon">

  <title>Coming Soon | Grumpy Company</title>

  <!-- Fav Icon -->
  <link rel="icon" type="image/x-icon" href="assets/imgs/logo/grumpy-favicon.png">

  <!-- Vendor CSS Files -->
  <link rel="stylesheet" href="assets/vendor/bootstrap.min.css">
  <link rel="stylesheet" href="assets/vendor/fontawesome.min.css">
  <link rel="stylesheet" href="assets/vendor/swiper-bundle.min.css">
  <link rel="stylesheet" href="assets/vendor/meanmenu.min.css">
  <link rel="stylesheet" href="assets/vendor/magnific-popup.css">
  <link rel="stylesheet" href="assets/vendor/animate.min.css">

  <!-- Template Main CSS File -->
  <link rel="stylesheet" href="assets/css/style.css">

  <!-- Custom Coming Soon Styles -->
  <style>
    .coming-soon-area {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f9e6dc;
      position: relative;
      overflow: hidden;
    }

    .coming-soon-content {
      text-align: center;
      position: relative;
      z-index: 2;
      max-width: 600px;
      padding: 0 20px;
    }

    .coming-soon-logo {
      margin-bottom: 40px;
      animation: fadeInUp 1s ease-out;
    }

    .coming-soon-logo img {
      max-width: 180px;
      height: auto;
    }

    .coming-soon-title {
      font-family: var(--font_thunder);
      font-size: clamp(48px, 8vw, 120px);
      font-weight: 700;
      line-height: 0.9;
      color: var(--primary);
      text-transform: uppercase;
      margin-bottom: 20px;
      animation: fadeInUp 1s ease-out 0.3s both;
    }

    .coming-soon-description {
      font-family: var(--font_dmsans);
      font-size: clamp(16px, 2vw, 18px);
      font-weight: 400;
      line-height: 1.6;
      color: #666666;
      margin-bottom: 40px;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
      animation: fadeInUp 1s ease-out 0.6s both;
    }



    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @media (max-width: 768px) {
      .coming-soon-logo img {
        max-width: 120px;
      }
    }
  </style>

</head>

<body class="body-wrapper font-heading-sequelsans-romanbody">

  <!-- Preloader -->
  <div id="preloader">
    <div id="container" class="container-preloader">
      <div class="animation-preloader">
        <div class="spinner"></div>
      </div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
  </div>

  <!-- Scroll to top -->
  <div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
      <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"></path>
    </svg>
  </div>

  <!-- Cursor Animation -->
  <div class="cursor-wrapper relative">
    <div class="cursor"></div>
    <div class="cursor-follower"></div>
  </div>

  <div class="has-smooth" id="has_smooth"></div>
  <div id="smooth-wrapper">
    <div id="smooth-content">

      <main>
        <!-- Coming Soon area start -->
        <section class="coming-soon-area">
          <div class="coming-soon-content">

            <div class="coming-soon-logo">
              <img src="assets/imgs/logo/grumpy-logo-black.png" alt="Grumpy Company">
            </div>

            <h1 class="coming-soon-title">Coming Soon</h1>

            <p class="coming-soon-description">
              We're working on something special.
              Stay tuned for updates! 🚀
            </p>

          </div>
        </section>
        <!-- Coming Soon area end -->

      </main>

    </div>
  </div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/jquery-3.7.1.min.js"></script>
  <script src="assets/vendor/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/jquery.magnific-popup.min.js"></script>
  <script src="assets/vendor/swiper-bundle.min.js"></script>
  <script src="assets/vendor/gsap.min.js"></script>
  <script src="assets/vendor/ScrollTrigger.min.js"></script>
  <script src="assets/vendor/ScrollSmoother.min.js"></script>
  <script src="assets/vendor/ScrollToPlugin.min.js"></script>
  <script src="assets/vendor/SplitText.min.js"></script>
  <script src="assets/vendor/TextPlugin.js"></script>
  <script src="assets/vendor/customEase.js"></script>
  <script src="assets/vendor/Flip.min.js"></script>
  <script src="assets/vendor/jquery.meanmenu.min.js"></script>
  <script src="assets/vendor/backToTop.js"></script>
  <script src="assets/vendor/matter.js"></script>
  <script src="assets/vendor/throwable.js"></script>
  <script src="assets/js/magiccursor.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>

  <!-- Custom Coming Soon JavaScript -->
  <script>
    // Simple fade-in animation on load
    document.addEventListener('DOMContentLoaded', function() {
      // Add a subtle hover effect to the logo
      const logo = document.querySelector('.coming-soon-logo img');

      logo.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
        this.style.transition = 'transform 0.3s ease';
      });

      logo.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
      });
    });
  </script>

</body>

</html>