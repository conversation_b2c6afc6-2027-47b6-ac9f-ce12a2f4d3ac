<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Font</title>
    <style>
        @font-face {
            font-family: "Thunder";
            src: url("assets/fonts/Thunder-BoldLC.ttf") format("truetype");
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }

        .test {
            font-family: "Thunder", Arial, sans-serif;
            font-size: 48px;
            font-weight: 700;
            color: red;
        }
    </style>
</head>
<body>
    <div class="test">THUNDER FONT TEST</div>
    
    <script>
        console.log('Page loaded');
        
        // Force font loading
        const testDiv = document.querySelector('.test');
        console.log('Computed font-family:', window.getComputedStyle(testDiv).fontFamily);
        
        // Check font loading
        document.fonts.ready.then(() => {
            console.log('Fonts ready');
            console.log('Thunder font check:', document.fonts.check('48px Thunder'));
        });
    </script>
</body>
</html>
