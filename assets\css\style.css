/*-----------------------------------------------------------------------------------

  Theme Name: <PERSON>ox - Creative Agency and Portfolio HTML Template
  Author: ravextheme
  Support: https://support.rrdevs.net/
  Description: Redox - Creative Agency and Portfolio HTML Template
  Version: 1.0

-----------------------------------------------------------------------------------

/************ TABLE OF CONTENTS ***************
variable css
typography css
animation css
global css
theme css
Preloader css
scroll css
button css
button animation css
menu css
modal css
cursor css
digital agency page css
design agency page css
creative agency page css
marketing agency page css
startup agency page css
portfolio agency page css
portfolio page css
parallax carousal page css
portfolio showcase page css
portfolio showcase 2 page css
404 page css
about page css
blog details page css
blog page css
contact page css
faq page css
service details page css
sercices page css
team details page css
work details page css
work page css

/*----------------------------------------*/
/* variable css  */
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Instrument+Sans:ital,wght@0,400..700;1,400..700&display=swap");
:root {
  --primary: #111111;
  --secondary: #555555;
  --border: rgba(17, 17, 17, 0.1);
  --bg: #111111;
  --theme: #FF6A3A;
  --black: #111111;
  --black-2: #999999;
  --white: #FFFFFF;
  --white-2: #999999;
  --action: #FF6A3A;
}
:root .dark {
  --primary: #ffffff;
  --secondary: #999999;
  --border: rgba(255, 255, 255, 0.1);
  --bg: #171717;
}

/*----------------------------------------*/
/* typography css  */
/*----------------------------------------*/
/* dm sans  */
/* instrument sans  */
/* Thunder  */
@font-face {
  font-family: "Thunder";
  src: url("../fonts/Thunder-BoldLC.ttf");
  font-weight: 700;
}
@font-face {
  font-family: "Thunder";
  src: url("../fonts/Thunder-SemiBoldLC.ttf");
  font-weight: 600;
}
@font-face {
  font-family: "Thunder";
  src: url("../fonts/Thunder-LC.ttf");
  font-weight: 400;
}
@font-face {
  font-family: "Thunder";
  src: url("../fonts/Thunder-MediumLC.ttf");
  font-weight: 500;
}
/* Sequel Sans  */
@font-face {
  font-family: "Sequel Sans Roman Body";
  src: url("../fonts/Sequel Sans Roman Body.otf");
  font-weight: 310;
}
@font-face {
  font-family: "Sequel Sans Medium Body";
  src: url("../fonts/Sequel Sans Medium Body.otf");
  font-weight: 315;
}
/* times now  */
@font-face {
  font-family: "TimesNow-SemiLightItalic";
  src: url("../fonts/TimesNow-SemiLightItalic.ttf");
  font-weight: 400;
}
/* bdo grotesk  */
@font-face {
  font-family: "BDOGrotesk-Regular";
  src: url("../fonts/BDOGrotesk-Regular.ttf");
  font-weight: 400;
}
@font-face {
  font-family: "BDOGrotesk-Regular";
  src: url("../fonts/BDOGrotesk-Medium.ttf");
  font-weight: 500;
}
@font-face {
  font-family: "BDOGrotesk-Regular";
  src: url("../fonts/BDOGrotesk-DemiBold.ttf");
  font-weight: 600;
}
/* tartufffo trial */
@font-face {
  font-family: "Tartuffo_Trial";
  src: url("../fonts/Tartuffo_Trial-Thin.otf");
  font-weight: 100;
}
@font-face {
  font-family: "Tartuffo_Trial";
  src: url("../fonts/Tartuffo_Trial-LightItalic.otf");
  font-weight: 300;
}
@font-face {
  font-family: "Tartuffo_Trial";
  src: url("../fonts/Tartuffo_Trial-Light.otf");
  font-weight: 300;
}
@font-face {
  font-family: "tartuffo-font-family-family";
  src: url("../fonts/Tartuffo_Trial-MediumItalic.otf");
  font-weight: 500;
}
:root {
  --font_dmsans: "DM Sans", sans-serif;
  --font_instrumentsans: "Instrument Sans", sans-serif;
  --font_thunder: "Thunder";
  --font_sequelsansromanbody: "Sequel Sans Roman Body";
  --font_sequelsansmediumbody: "Sequel Sans Medium Body";
  --font_timesnow: "TimesNow-SemiLightItalic";
  --font_bdogrotesk: "BDOGrotesk-Regular";
  --font_tartuffo: "tartuffo-font-family";
  --font_tartuffotrial: "Tartuffo_Trial";
  --font_awesome: "Font Awesome 6 Free";
}

.font-heading-instrumentsans-medium h1,
.font-heading-instrumentsans-medium h2,
.font-heading-instrumentsans-medium h3,
.font-heading-instrumentsans-medium h4,
.font-heading-instrumentsans-medium h5,
.font-heading-instrumentsans-medium h6 {
  font-family: var(--font_instrumentsans);
}
.font-heading-sequelsans-romanbody h1,
.font-heading-sequelsans-romanbody h2,
.font-heading-sequelsans-romanbody h3,
.font-heading-sequelsans-romanbody h4,
.font-heading-sequelsans-romanbody h5,
.font-heading-sequelsans-romanbody h6 {
  font-family: var(--font_sequelsansromanbody);
}
.font-heading-thunder-regular h1,
.font-heading-thunder-regular h2,
.font-heading-thunder-regular h3,
.font-heading-thunder-regular h4,
.font-heading-thunder-regular h5,
.font-heading-thunder-regular h6 {
  font-family: var(--font_thunder);
}
.font-heading-bdogrotesk-regular h1,
.font-heading-bdogrotesk-regular h2,
.font-heading-bdogrotesk-regular h3,
.font-heading-bdogrotesk-regular h4,
.font-heading-bdogrotesk-regular h5,
.font-heading-bdogrotesk-regular h6 {
  font-family: var(--font_bdogrotesk);
}
.font-heading-tartuffotrial-thin h1,
.font-heading-tartuffotrial-thin h2,
.font-heading-tartuffotrial-thin h3,
.font-heading-tartuffotrial-thin h4,
.font-heading-tartuffotrial-thin h5,
.font-heading-tartuffotrial-thin h6 {
  font-family: var(--font_tartuffotrial);
}

* {
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font_dmsans);
  line-height: 1;
}

html {
  scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  padding: 0;
  margin: 0;
  color: var(--primary);
  line-height: 1.22;
  font-family: var(--font_instrumentsans);
  font-weight: 500;
}

ul,
ol {
  padding: 0;
  margin: 0;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
  transition: all 0.3s;
  color: inherit;
}
a:hover {
  color: var(--primary);
}

button {
  background-color: transparent;
  border: 0;
}

p {
  padding: 0;
  margin: 0;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  color: var(--secondary);
}
@media only screen and (max-width: 1399px) {
  p {
    font-size: 20px;
    line-height: 28px;
  }
}

strong {
  font-weight: 500;
}

video,
iframe,
img {
  margin: 0;
  padding: 0;
}

img {
  max-width: 100%;
}

.medium {
  font-weight: 600;
}

.bold {
  font-weight: 700;
}

@media only screen and (max-width: 767px) {
  .g-0 {
    padding-right: 15px;
    padding-left: 15px;
  }
  .row.g-0 {
    padding-right: 0;
    padding-left: 0;
  }
  br {
    display: none;
  }
}
main {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

h1 {
  font-size: 48px;
}

h2 {
  font-size: 36px;
}

h3 {
  font-size: 32px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

/*----------------------------------------*/
/* animation css */
/*----------------------------------------*/
.t_parallax_image {
  overflow: hidden;
}

@keyframes t-Bubble {
  0% {
    scale: 1;
  }
  50% {
    scale: 1.5;
  }
  100% {
    scale: 1;
  }
}
@keyframes t-Zoom {
  0% {
    scale: 1;
  }
  50% {
    scale: 0.5;
  }
  100% {
    scale: 1;
  }
}
@keyframes t-Zoom_2 {
  0% {
    scale: 1;
  }
  50% {
    scale: 0.9;
  }
  100% {
    scale: 1;
  }
}
@keyframes t-SlideBottom {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(50px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes t-reveal {
  to {
    opacity: 1;
    filter: blur(0px);
  }
}
@keyframes t-fadeUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(50px);
    -ms-transform: translateY(50px);
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes t-spinner {
  to {
    transform: rotateZ(360deg);
  }
}
@keyframes t-characters {
  0%, 75%, 100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%, 50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes t-sheen {
  50% {
    transform: translateY(-20px);
    color: var(--primary);
  }
}
@keyframes t-slide {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}
/*----------------------------------------*/
/* global css */
/*----------------------------------------*/
.body-overlay {
  position: fixed;
  z-index: 9;
  pointer-events: none;
  top: 0;
  opacity: 1;
  inset-inline-start: 0;
  width: 100vw;
  height: 100vh;
  background-repeat: repeat;
  background-position: top left;
  background-image: url(../imgs/writer/body-bg.webp);
}

.container-xl {
  max-width: 1550px;
}

.rr-container-1405 {
  max-width: 1405px;
}

.text-slider-active .swiper-slide {
  width: auto;
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: #d6d6d6;
}

::-webkit-scrollbar-thumb {
  background: #888;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* SVG Logo Fixes */
svg {
  max-width: 100%;
  height: auto;
}
.header__logo svg,
.offset-logo svg,
.footer-logo svg {
  vertical-align: middle;
}

/* Debug styles for main header SVG - remove after testing */
.header-area .header__logo svg {
  border: 2px solid red !important;
  background: rgba(255, 255, 0, 0.3) !important;
  min-width: 50px !important;
  min-height: 30px !important;
}

.pos-abs {
  position: absolute;
}

.circle-text {
  width: 140px;
  height: 140px;
  position: relative;
  border-radius: 100px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px;
}
@media only screen and (max-width: 991px) {
  .circle-text {
    width: 120px;
    height: 120px;
  }
}
.circle-text .text {
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: var(--primary);
  position: absolute;
  -webkit-animation: textRotation 8s linear infinite;
  animation: textRotation 8s linear infinite;
}
@-webkit-keyframes textRotation {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes textRotation {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.circle-text .text span {
  left: 50%;
  top: 0px;
  font-size: 14px;
  text-transform: uppercase;
  position: absolute;
  transform-origin: 0 65px;
}
.circle-text .icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.circle-text:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  border: 37px solid transparent;
  border-radius: 50%;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.fix {
  overflow: hidden;
}

.bg-full {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.bg-full img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.border-top-bottom {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
}

.has-top-line {
  position: relative;
  padding-top: 10px;
}
.has-top-line:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  background-color: currentColor;
  top: 0;
  left: 0;
}

.has-bottom-line {
  position: relative;
  padding-bottom: 10px;
}
.has-bottom-line:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  background-color: currentColor;
  bottom: 0;
  left: 0;
}

.has-left-line {
  position: relative;
  padding-inline-start: 35px;
  display: inline-block;
}
.has-left-line:before {
  position: absolute;
  content: "";
  width: 30px;
  height: 1px;
  background-color: currentColor;
  inset-inline-start: 0;
  top: 50%;
  transform: translateY(-50%);
}

.has-right-line {
  position: relative;
  padding-inline-end: 35px;
  display: inline-block;
}
.has-right-line:after {
  position: absolute;
  content: "";
  width: 30px;
  height: 1px;
  background-color: currentColor;
  inset-inline-end: 0;
  top: 50%;
  transform: translateY(-50%);
}

.t-btn-play {
  width: 56px;
  height: 56px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1.5px solid currentColor;
  color: var(--primary);
  border-radius: 50%;
  transition: all 0.5s;
  font-size: 14px;
}
@media only screen and (max-width: 767px) {
  .t-btn-play {
    width: 50px;
    height: 50px;
    font-size: 12px;
    border-width: 1px;
  }
}
.t-btn-play:hover {
  color: var(--theme, --action);
}
.t-btn-play.light {
  color: var(--white);
}
.t-btn-play.light:hover {
  color: var(--white);
}
.t-btn-play.dark {
  color: var(--black);
}
.t-btn-play.dark:hover {
  color: var(--black);
}

.show-light {
  display: inline-block;
}

.show-dark {
  display: none;
}

.line-area {
  position: relative;
}

.lines {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
}
.lines .line {
  width: 1px;
  height: 100%;
  background-color: rgba(0, 81, 83, 0.031372549);
  display: inline-block;
  position: relative;
  z-index: 1;
}

.t-btn-icon i {
  transform: rotate(-45deg);
  transition: all 0.3s;
  font-size: 20px;
  color: var(--primary);
}
.t-btn-icon:hover i {
  transform: rotate(0);
}

.list-check li {
  position: relative;
  padding-inline-start: 30px;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.4;
  margin-bottom: 10px;
}
.list-check li::before {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  background-image: url("../imgs/electrician/check-mark.webp");
  background-repeat: no-repeat;
  width: 14px;
  height: 14px;
  top: 4px;
  transform: rotateY(0deg);
}
.list-check li:last-child {
  margin-bottom: 0;
}

.list-plus li {
  position: relative;
  padding-inline-start: 30px;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.4;
  display: inline-block;
  width: 100%;
}
.list-plus li::before {
  content: "+";
  position: absolute;
  inset-inline-start: 0;
  top: -10px;
  font-size: 30px;
  font-weight: 300;
  line-height: 1;
}
.list-plus li:not(:last-child) {
  margin-bottom: 10px;
}

.pos-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.pagination-with-dash {
  font-size: 14px;
  display: flex;
  gap: 10px;
  color: var(--primary);
  align-items: center;
}
.pagination-with-dash .dash {
  width: 100px;
  height: 1px;
  background-color: var(--primary);
}
.pagination-with-dash .swiper-pagination-current {
  display: flex;
  gap: 5px;
  align-items: center;
}
.pagination-with-dash .swiper-pagination-current:before {
  content: url(../imgs/icon/arrow-left.webp);
}
.pagination-with-dash .swiper-pagination-total {
  display: flex;
  gap: 5px;
  align-items: center;
}
.pagination-with-dash .swiper-pagination-total:after {
  content: url(../imgs/icon/arrow-right.webp);
}

.t__toggle_switcher .slide-toggle-wrapper {
  display: flex;
  justify-content: center;
}
.t__toggle_switcher .slide-toggle-btn {
  --switcher-width: 40px;
  --switcher-border-width: 2px;
  --switcher-indicator-width: 16px;
  background-color: #F0F7F8;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  user-select: none;
}
.t__toggle_switcher .slide-toggle-btn input {
  display: none;
}
.t__toggle_switcher .before_label,
.t__toggle_switcher .after_label {
  cursor: pointer;
  font-size: 18px;
  color: var(--primary);
}
.t__toggle_switcher .toggle-pane {
  display: none;
}
.t__toggle_switcher .toggle-pane.show {
  display: block;
}
.t__toggle_switcher.style-1 .switcher {
  display: inline-block;
  width: var(--switcher-width);
  height: 20px;
  background-color: #999999;
  border: var(--switcher-border-width) solid #999999;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
}
.t__toggle_switcher.style-1 .switcher::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: var(--switcher-indicator-width);
  height: var(--switcher-indicator-width);
  background-color: #ffffff;
  border-radius: 50%;
  transition: transform 0.3s;
  transform: translate(0px, -50%);
}
.t__toggle_switcher.style-1 input:checked + .switcher::before {
  transform: translate(calc(var(--switcher-width) - (var(--switcher-indicator-width) + 2 * var(--switcher-border-width))), -50%);
}
.t__toggle_switcher.style-1 input:checked + .switcher {
  background-color: #000;
  border-color: #000;
}
.t__toggle_switcher.style-2 .before_label,
.t__toggle_switcher.style-2 .after_label {
  padding: 22px 38px;
  position: relative;
  z-index: 2;
  font-size: 16px;
  line-height: 1;
  color: var(--primary);
}
.t__toggle_switcher.style-2 .before_label:after,
.t__toggle_switcher.style-2 .after_label:after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 60px;
  position: absolute;
  left: 0;
  top: 0;
  background-color: var(--theme);
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: transform 0.3s;
}
.t__toggle_switcher.style-2 .before_label.active,
.t__toggle_switcher.style-2 .after_label.active {
  color: var(--primary);
}
.t__toggle_switcher.style-2 .before_label.active:after,
.t__toggle_switcher.style-2 .after_label.active:after {
  opacity: 1;
  visibility: visible;
  transform: translatex(0);
}
.t__toggle_switcher.style-2 .before_label:after {
  transform: translatex(100%);
}
.t__toggle_switcher.style-2 .after_label:after {
  transform: translatex(-100%);
}
.t__toggle_switcher.style-2 .slide-toggle-btn {
  gap: 0;
  border-radius: 60px;
}

.parallax-view {
  overflow: hidden;
}

.hover-reveal {
  position: relative;
  overflow: hidden;
  cursor: none;
}
.hover-reveal:hover > *:first-child {
  opacity: 1 !important;
}
.hover-reveal > *:first-child {
  opacity: 0;
  z-index: 1;
}

.section-spacing {
  padding-top: 100px;
  padding-bottom: 100px;
}
@media only screen and (max-width: 1919px) {
  .section-spacing {
    padding-top: 90px;
    padding-bottom: 90px;
  }
}
@media only screen and (max-width: 1399px) {
  .section-spacing {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .section-spacing {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.section-spacing-top {
  padding-top: 100px;
}
@media only screen and (max-width: 1919px) {
  .section-spacing-top {
    padding-top: 90px;
  }
}
@media only screen and (max-width: 1399px) {
  .section-spacing-top {
    padding-top: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .section-spacing-top {
    padding-top: 60px;
  }
}

.section-spacing-bottom {
  padding-bottom: 100px;
}
@media only screen and (max-width: 1919px) {
  .section-spacing-bottom {
    padding-bottom: 90px;
  }
}
@media only screen and (max-width: 1399px) {
  .section-spacing-bottom {
    padding-bottom: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .section-spacing-bottom {
    padding-bottom: 60px;
  }
}

.container {
  --bs-gutter-x: 30px;
}
@media (min-width: 1600px) {
  .container.full-hd {
    max-width: 1920px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0;
    padding-right: 0;
  }
}

.section-subtitle {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--primary);
}

.section-title {
  font-size: 100px;
}
@media only screen and (max-width: 1919px) {
  .section-title {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .section-title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .section-title {
    font-size: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .section-title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title {
    font-size: 35px;
  }
}
.section-title.font-instrumentsans-medium {
  font-family: var(--font_instrumentsans);
  font-weight: 500;
  line-height: 0.9;
}
.section-title.font-sequelsans-romanbody {
  font-family: var(--font_sequelsansromanbody);
  font-weight: 310;
  line-height: 0.9;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 991px) {
  .section-title.font-sequelsans-romanbody {
    line-height: 1.1;
  }
}
.section-title.font-thunder-regular {
  font-family: var(--font_thunder);
  font-weight: 400;
  line-height: 0.85;
  text-transform: uppercase;
}
.section-title.font-thunder-regular span {
  font-family: var(--font_timesnow);
  color: rgba(17, 17, 17, 0.4);
  display: inline-block;
  line-height: 0;
}
.section-title.font-bdogrotesk-regular {
  font-family: var(--font_bdogrotesk);
  font-weight: 400;
  line-height: 0.95;
  letter-spacing: -0.05em;
}
@media only screen and (max-width: 991px) {
  .section-title.font-bdogrotesk-regular {
    line-height: 1.1;
  }
}
.section-title.font-tartuffotrial-thin {
  font-family: var(--font_tartuffotrial);
  font-weight: 100;
  line-height: 1;
}

/* inverted text style  */
.text-invert > div {
  background-image: linear-gradient(to right, var(--primary) 50%, #CDC9C6 51%);
  background-size: 200% 100%;
  background-position-x: 100%;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}

.offcanvas-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #000;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s;
}

.offcanvas-overlay.overlay-open {
  opacity: 0.5;
  visibility: visible;
}

.side-info-close {
  font-size: 18px;
  padding: 0;
  transition: all 0.3s linear;
  background-color: var(--white);
  color: var(--black);
  width: 40px;
  height: 40px;
  border: 1px solid var(--black);
  border-radius: 50%;
  line-height: 38px;
}
.dark .side-info-close {
  background-color: #292828;
  color: var(--white);
}
.side-info-close:hover {
  transform: rotate(90deg);
}

.side-info {
  background: var(--white) none repeat scroll 0 0;
  padding: 40px 45px;
  position: fixed;
  right: 0;
  top: 0;
  width: 500px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 9999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.dark .side-info {
  background-color: var(--black);
}
.side-info ::-webkit-scrollbar {
  display: none;
}
@media (max-width: 575px) {
  .side-info {
    width: 100%;
    padding: 30px 30px;
  }
}
.side-info.info-open {
  opacity: 1;
  transform: translateX(0);
}

.offset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.offset-logo {
  width: 120px;
}
@media (max-width: 575px) {
  .offset-logo {
    width: 100px;
  }
}
.offset-logo img {
  max-width: 120px;
  height: auto;
}
.offset-logo svg {
  max-width: 120px;
  height: auto;
  display: block;
}
.dark .offset-logo .light-logo {
  display: none;
}
.offset-logo .dark-logo {
  display: none;
}
.dark .offset-logo .dark-logo {
  display: block;
}
.offset-button {
  margin-top: 40px;
}
@media (min-width: 576px) {
  .offset-button {
    display: none;
  }
}

.offset-widget-box {
  margin-top: 40px;
}
.offset-widget-box .title {
  font-size: 24px;
  line-height: 1.33;
  margin-bottom: 15px;
}
@media only screen and (max-width: 1919px) {
  .offset-widget-box .title {
    margin-bottom: 22px;
  }
}
.offset-widget-box .contact-meta > *:not(:first-child) {
  margin-top: 16px;
}
.offset-widget-box .contact-item {
  display: flex;
  align-items: center;
  gap: 14px;
}
.offset-widget-box .contact-item span {
  color: var(--primary);
  font-weight: 500;
}
.offset-widget-box .contact-item span a:hover {
  color: var(--secondary);
}
.offset-widget-box .contact-item .icon {
  width: 40px;
  min-width: 40px;
  height: 40px;
  display: inline-flex;
  border: 1px solid var(--primary);
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: var(--primary);
}

/* mean menu customize */
.mobile-menu {
  margin-top: 40px;
}
.mobile-menu.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}
.mobile-menu.mean-container .mean-nav > ul > li:last-child > a {
  border-bottom: 1px solid var(--border);
}
.mobile-menu.mean-container .mean-nav {
  background: none;
  margin-top: 0;
}
.mobile-menu.mean-container .mean-nav .new {
  font-size: 10px;
  font-weight: 600;
  background: #FFA38E;
  color: var(--black-2);
  padding: 3px 7px;
  line-height: 1;
  display: flex;
  align-items: center;
  border-radius: 2px;
}
.mobile-menu.mean-container .mean-nav ul li a {
  width: 100%;
  padding: 15px 0;
  padding-inline-start: 0px;
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  color: var(--primary);
  text-transform: capitalize;
  border-top: 1px solid var(--border);
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  align-items: center;
  outline: none;
  transform: translateY(var(--y)) translateZ(0);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  box-sizing: border-box;
  opacity: 1;
}
@media only screen and (max-width: 767px) {
  .mobile-menu.mean-container .mean-nav ul li a {
    font-size: 20px;
  }
}
.mobile-menu.mean-container .mean-nav ul li a:hover {
  color: var(--secondary);
}
.mobile-menu.mean-container .mean-nav ul li a.mean-expand {
  width: 100%;
  height: 48px;
  justify-content: end;
  font-weight: 300;
  border: none !important;
  background: transparent;
  color: var(--primary);
}
@media only screen and (max-width: 767px) {
  .mobile-menu.mean-container .mean-nav ul li a.mean-expand {
    height: 50px;
  }
}
.mobile-menu.mean-container .mean-nav ul li a.mean-expand:hover {
  opacity: 1;
}
.mobile-menu.mean-container .mean-nav ul li li:first-child {
  border-top: 1px solid var(--border);
}
.mobile-menu.mean-container .mean-nav ul li li a {
  font-size: 16px;
  text-transform: capitalize;
  border-top: none !important;
  padding: 12px 0;
  padding-inline-start: 15px;
}
.mobile-menu.mean-container .mean-nav ul li li a.mean-expand {
  height: 58px;
}
@media only screen and (max-width: 991px) {
  .mobile-menu.mean-container .mean-nav ul li li a.mean-expand {
    height: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .mobile-menu.mean-container .mean-nav ul li li a.mean-expand {
    height: 22px;
  }
}
.mobile-menu.mean-container .mean-nav ul li li li:last-child {
  border-bottom: 1px solid var(--border);
}
.mobile-menu.mean-container .mean-nav ul li li li a {
  padding-left: 40px;
}
.mobile-menu.mean-container .mean-bar {
  padding: 0;
  background: none;
  max-height: auto;
  overflow-y: scroll;
}
.mobile-menu.mean-container .mean-bar::-webkit-scrollbar {
  width: 0;
}
.mobile-menu.mean-container a.meanmenu-reveal {
  display: none !important;
}

/* body style  */
.body-page-inner {
  position: relative;
  z-index: 100;
  background-color: #FFFFFF;
}
.body-page-inner.dark .header-area-2 .side-toggle {
  background-color: #1D1C1C;
}
@media (min-width: 1650px) {
  .body-page-inner .container.large {
    max-width: 1650px;
    --container-max-widths: 1620px;
    --bs-gutter-x: 30px;
  }
}
.body-page-inner .header-area-2 .side-toggle {
  background-color: rgb(243, 243, 243);
}
.dark .body-page-inner .header-area-2 .side-toggle {
  background-color: #1D1C1C;
}
.body-page-inner .header-area-2__inner {
  border-bottom: 1px solid var(--border);
}

.header-sticky {
  transition: all 0.5s;
}

.transformed {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 800;
  transform: translateY(-100%);
}
.transformed .header-area__inner {
  height: 80px;
}

.sticky {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 800;
  background-color: #FFFFFF;
  transform: translateY(0%);
}
.sticky .header-area__inner {
  height: 80px;
}

/* page title style  */
.page-title-wrapper {
  margin-top: 68px;
  margin-bottom: 28px;
}
@media only screen and (max-width: 991px) {
  .page-title-wrapper {
    margin-bottom: 10px;
  }
}
.page-title-wrapper .page-title {
  font-family: var(--font_thunder);
  font-size: 495px;
  font-weight: 600;
  line-height: 0.8;
  letter-spacing: -0.02em;
  text-align: center;
  text-transform: uppercase;
}
@media only screen and (max-width: 1919px) {
  .page-title-wrapper .page-title {
    font-size: 385px;
  }
}
@media only screen and (max-width: 1399px) {
  .page-title-wrapper .page-title {
    font-size: 325px;
  }
}
@media only screen and (max-width: 1199px) {
  .page-title-wrapper .page-title {
    font-size: 265px;
  }
}
@media only screen and (max-width: 991px) {
  .page-title-wrapper .page-title {
    font-size: 185px;
  }
}
@media only screen and (max-width: 767px) {
  .page-title-wrapper .page-title {
    font-size: 125px;
  }
}
@media (max-width: 575px) {
  .page-title-wrapper .page-title {
    font-size: 90px;
  }
}

/*----------------------------------------*/
/* theme css  */
/*----------------------------------------*/
html {
  --container-max-widths: 1320px;
}
@media only screen and (max-width: 1399px) {
  html {
    --container-max-widths: 1140px;
  }
}
@media only screen and (max-width: 1199px) {
  html {
    --container-max-widths: 960px;
  }
}
@media only screen and (max-width: 991px) {
  html {
    --container-max-widths: 720px;
  }
}
@media only screen and (max-width: 767px) {
  html {
    --container-max-widths: 540px;
  }
}

body {
  background-color: #F8F8F8;
  color: var(--secondary);
}

.body-wrapper {
  background-color: var(--white);
}
.body-wrapper.dark {
  background-color: var(--black);
}

.img_anim_reveal {
  visibility: hidden;
  overflow: hidden;
}
.img_anim_reveal img {
  object-fit: cover;
  transform-origin: left;
}

.anim-reveal {
  overflow: hidden;
}

.anim-reveal-line {
  overflow: hidden;
}

.color-white {
  color: var(--white);
}
.color-black {
  color: var(--black);
}
.color-primary {
  color: var(--primary);
}
.color-secondary {
  color: var(--secondary);
}

.bg-white {
  background-color: var(--white);
}
.bg-black {
  background-color: var(--black);
}
.bg-primary {
  background-color: var(--primary);
}
.bg-secondary {
  background-color: var(--secondary);
}
.bg-transparent {
  background-color: transparent !important;
}
.bg-theme {
  background-color: var(--theme) !important;
}

.zi-1 {
  z-index: 1;
}
.zi-2 {
  z-index: 2;
}
.zi-0 {
  z-index: 0;
}
.zi--1 {
  z-index: -1;
}

.text-underline {
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 5px;
  text-decoration-skip-ink: none;
}

.header__area-6 {
  position: unset;
}

.vertically-center {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.text-indent-40 {
  text-indent: 40px;
}
.text-indent-50 {
  text-indent: 50px;
}

header {
  margin-bottom: -1px;
  z-index: 100;
}

section {
  margin-bottom: -1px;
}

.mb--1 {
  margin-bottom: -5px;
}

.dir-rtl {
  direction: rtl;
}

.show-dark {
  display: inline-block;
}

.show-light {
  display: none;
}

.line-divider-sm {
  height: 0.5px;
  background-color: var(--black-9);
}

.admin-bar header,
.admin-bar .body-wrapper {
  margin-top: 32px;
}

.swiper,
.swiper-container {
  direction: ltr;
}

.border-e-0 {
  border-inline-end: 0 !important;
}

.border-s-0 {
  border-inline-start: 0 !important;
}

/* Preloader css */
.container-preloader {
  align-items: center;
  cursor: none;
  display: flex;
  width: 100vw;
  height: 100vh;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 900;
}
.container-preloader .animation-preloader {
  position: absolute;
  z-index: 100;
}
.container-preloader .animation-preloader .spinner {
  animation: t-spinner 1s infinite linear;
  border-radius: 50%;
  height: 9em;
  width: 9em;
  border: 10px solid var(--primary);
  border-top-color: var(--white);
  margin: 0 auto 3.5em auto;
}
.dark .container-preloader .animation-preloader .spinner {
  border-top-color: var(--black);
}
@media only screen and (max-width: 1919px) {
  .container-preloader .animation-preloader .spinner {
    height: 5em;
    width: 5em;
    border-width: 5px;
  }
}
@media only screen and (max-width: 767px) {
  .container-preloader .animation-preloader .spinner {
    margin: 0 auto 0.2em auto;
  }
}
.container-preloader .animation-preloader .txt-loading {
  font: bold 5em "Montserrat", sans-serif;
  text-align: center;
  user-select: none;
}
.container-preloader .animation-preloader .txt-loading .characters {
  color: var(--white);
  position: relative;
  display: inline-block;
}
@media only screen and (max-width: 767px) {
  .container-preloader .animation-preloader .txt-loading .characters {
    font-size: 50px;
  }
}
.container-preloader .animation-preloader .txt-loading .characters:before {
  color: var(--primary);
  content: attr(data-text);
  animation: t-characters 4s infinite;
  left: 0;
  top: 0;
  opacity: 0;
  position: absolute;
  transform: rotateY(-90deg);
}
.container-preloader .animation-preloader .txt-loading .characters:nth-child(2):before {
  animation-delay: 0.2s;
}
.container-preloader .animation-preloader .txt-loading .characters:nth-child(3):before {
  animation-delay: 0.4s;
}
.container-preloader .animation-preloader .txt-loading .characters:nth-child(4):before {
  animation-delay: 0.6s;
}
.container-preloader .animation-preloader .txt-loading .characters:nth-child(5):before {
  animation-delay: 0.8s;
}
.container-preloader .animation-preloader .txt-loading .characters:nth-child(6):before {
  animation-delay: 1s;
}
.container-preloader .loader-section {
  background-color: var(--white);
  height: 100%;
  position: fixed;
  top: 0;
  width: calc(50% + 1px);
}
.dark .container-preloader .loader-section {
  background-color: var(--black);
}
.container-preloader .loader-section.section-left {
  left: 0;
}
.container-preloader .loader-section.section-right {
  right: 0;
}

.loaded .animation-preloader {
  opacity: 0;
  transition: 0.3s ease-out;
}
.loaded .loader-section.section-left {
  transform: translateX(-101%);
  transition: 0.7s 0.3s all cubic-bezier(0.1, 0.1, 0.1, 1);
}
.loaded .loader-section.section-right {
  transform: translateX(101%);
  transition: 0.7s 0.3s all cubic-bezier(0.1, 0.1, 0.1, 1);
}

/* scroll css */
.scroll__down {
  display: flex;
  gap: 20px;
  align-items: center;
}
.scroll__down p {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.9;
  text-transform: uppercase;
  color: var(--white);
}
.scroll__down span {
  width: 66px;
  height: 106px;
  border: 1px solid var(--black-6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 89px;
}
.scroll__down span i {
  color: var(--white);
}
.scroll__down-wrapper {
  height: 425px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (max-width: 1399px) {
  .scroll__down-wrapper {
    height: 380px;
  }
}
@media only screen and (max-width: 1199px) {
  .scroll__down-wrapper {
    height: 350px;
  }
}
@media only screen and (max-width: 767px) {
  .scroll__down-wrapper {
    height: auto;
    padding: 40px 0;
  }
}
.scroll-top {
  width: 50px;
  height: 50px;
  position: fixed;
  right: 15px;
  bottom: 0px;
  z-index: 9999;
  background: var(--white);
  border-radius: 100px;
  mix-blend-mode: exclusion;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s;
}
.scroll-top.showed {
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}

.go-top-writer {
  width: 105px;
  font-size: 16px;
  cursor: pointer;
  text-align: left;
  color: var(--white);
  background-image: url(../imgs/writer/go-top.webp);
  background-position: right center;
  background-repeat: no-repeat;
  right: 16%;
  visibility: hidden;
  opacity: 0;
  z-index: 9;
  transition: all 0.5s;
}
.go-top-writer:hover {
  color: var(--primary);
}
.go-top-writer.showed {
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}
@media only screen and (max-width: 767px) {
  .go-top-writer br {
    display: block;
  }
}

.progress-wrap {
  position: fixed;
  right: 20px;
  bottom: 20px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-100px);
  transition: all 300ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  mix-blend-mode: exclusion;
  background-color: var(--black);
}

.progress-wrap::after {
  position: absolute;
  content: "\f062";
  font: var(--fa-font-solid);
  text-align: center;
  line-height: 46px;
  font-size: 20px;
  color: var(--primary);
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
  border-radius: 50px;
}
.dark .progress-wrap::after {
  color: var(--black);
}

.progress-wrap svg path {
  fill: var(--black-6);
}

.progress-wrap svg.progress-circle path {
  fill: var(--white);
  stroke: transparent;
  stroke-width: 5;
  box-sizing: border-box;
  transition: all 200ms linear;
}

.light .scroll__down p {
  color: var(--black);
}
.light .scroll__down span {
  border-color: var(--white-3);
}
.light .scroll__down span i {
  color: var(--black);
}
.light.go-top-writer {
  color: var(--black);
  background-color: transparent;
  background-image: url(../imgs/writer/go-top-light.webp);
}
.light.progress-wrap {
  margin: 0;
  background-color: transparent;
}
.light.progress-wrap svg path {
  fill: var(--black);
}
.light.progress-wrap::after {
  color: var(--white);
}

/*----------------------------------------*/
/* button css  */
/*----------------------------------------*/
.rr-btn-group {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  font-family: var(--font_dmsans);
}
.rr-btn-group.btn-whte .b {
  border: 1px solid white;
  color: var(--white);
}
.rr-btn-group.btn-whte .c {
  border: 1px solid white;
  color: var(--white);
}

.rr-btn-group span {
  letter-spacing: 0;
}
.rr-btn-group:hover .b {
  transform: rotate(-20deg);
}
.rr-btn-group:hover .c {
  transform: translate(-7px, 0px);
}

.rr-btn-group .b {
  padding: 9px 25px;
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  color: var(--primary);
  background-color: transparent;
  border: 2px solid var(--primary);
  border-radius: 50px;
  transition: all 0.3s;
}

.rr-btn-group .c {
  padding: 9px 11px;
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  color: var(--primary);
  background-color: transparent;
  border: 2px solid var(--primary);
  border-radius: 50px;
  transition: all 0.3s;
}
.rr-btn-group .c i {
  rotate: -30deg;
}

.rr-btn {
  justify-content: center;
  position: relative;
  overflow: hidden;
  z-index: 5;
  padding: 26px 42px;
  background-color: var(--primary);
  color: var(--white);
  border: 1px solid var(--primary);
  border-radius: 100px;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  text-transform: capitalize;
  letter-spacing: -0.02em;
}
.dark .rr-btn {
  color: var(--black);
}
@media only screen and (max-width: 1199px) {
  .rr-btn {
    padding: 18px 29px;
  }
}
.rr-btn:hover::before, .rr-btn:focus::before {
  height: 100%;
}
.rr-btn:hover .btn-wrap .text-one, .rr-btn:focus .btn-wrap .text-one {
  transform: translateY(-150%);
}
.rr-btn:hover .btn-wrap .text-two, .rr-btn:focus .btn-wrap .text-two {
  top: 50%;
  transform: translateY(-50%);
  color: var(--black);
}
.dark .rr-btn:hover .btn-wrap .text-two, .dark .rr-btn:focus .btn-wrap .text-two {
  color: var(--white);
}
.rr-btn:after {
  display: block;
  clear: both;
  content: "";
}
.rr-btn::before {
  background-color: var(--white);
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  position: absolute;
  transition: all 0.5s;
}
.dark .rr-btn::before {
  background-color: var(--black);
}
.rr-btn .btn-wrap {
  z-index: 1;
  overflow: hidden;
  position: relative;
  display: inline-block;
  border: none;
}
.rr-btn .btn-wrap .text-one,
.rr-btn .btn-wrap .text-two {
  display: flex;
  align-items: center;
}
.rr-btn .btn-wrap .text-one {
  position: relative;
  display: block;
  color: var(--white);
  transition: all 0.5s;
}
.dark .rr-btn .btn-wrap .text-one {
  color: var(--black);
}
.rr-btn .btn-wrap .text-two {
  position: absolute;
  top: 100%;
  display: block;
  color: var(--white);
  transition: all 0.5s;
}
.dark .rr-btn .btn-wrap .text-two {
  color: var(--black);
}
.rr-btn.btn-border {
  border: 1px solid rgba(17, 17, 17, 0.15);
  background-color: transparent;
  color: var(--primary);
  padding: 25px 42px;
}
.dark .rr-btn.btn-border {
  border-color: rgba(255, 255, 255, 0.15);
}
.rr-btn.btn-border:hover, .rr-btn.btn-border:focus {
  border-color: transparent;
  color: white;
}
.rr-btn.btn-border:hover .text-two, .rr-btn.btn-border:focus .text-two {
  color: #F9F9F9;
}
.rr-btn.btn-border .btn-wrap .text-one {
  color: var(--primary);
}
.rr-btn.btn-border .btn-wrap .text-two {
  color: var(--white);
}
.rr-btn.btn-border-white {
  border: 1px solid rgba(252, 247, 243, 0.1);
  background-color: transparent;
}
.rr-btn.hover-bg-theme {
  border-width: 0;
}
.rr-btn.hover-bg-theme:hover .btn-wrap .text-two, .rr-btn.hover-bg-theme:focus .btn-wrap .text-two {
  color: var(--white);
}
.rr-btn.hover-bg-theme::before {
  background-color: var(--theme);
}
.rr-btn.hover-bg-theme.btn-border {
  border-width: 1px;
}

.rr-btn-underline {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  color: var(--primary);
  text-transform: uppercase;
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 4px;
  white-space: nowrap;
}
.rr-btn-underline:hover::before {
  width: 0;
}
.rr-btn-underline::before {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  bottom: 0px;
  width: 100%;
  height: 2px;
  background-color: currentColor;
  transition: 0.3s;
}
.rr-btn-underline i {
  font-size: 10px;
}

.rr-hover-btn-wrapper {
  display: inline-block;
}

.rr-btn-circle {
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 170px;
  height: 170px;
  border-radius: 50%;
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  text-transform: uppercase;
  color: var(--primary);
  border: 1px solid rgba(17, 17, 17, 0.2);
  z-index: 1;
}
.dark .rr-btn-circle {
  border-color: rgba(255, 255, 255, 0.2);
}

.rr-btn-circle:hover {
  color: var(--white);
  border-color: transparent;
}
.dark .rr-btn-circle:hover {
  color: var(--black);
}

.rr-btn-circle:hover .rr-btn-circle-dot {
  width: 400px;
  height: 400px;
}

.rr-btn-circle-dot {
  position: absolute;
  width: 1px;
  height: 1px;
  background-color: var(--primary);
  line-height: 20px;
  border-radius: 50%;
  -webkit-transition: all 0.5s ease-out;
  -moz-transition: all 0.5s ease-out;
  -ms-transition: all 0.5s ease-out;
  -o-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
}

/* button animation css */
@keyframes mask_animation {
  from {
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
    mask-position: 100% 0;
  }
}
@keyframes mask_animation_2 {
  from {
    -webkit-mask-position: 100% 0;
    mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
  }
}
.btn-hover-default {
  transition: all 0.5s;
}
.btn-hover-default:hover {
  color: var(--black);
  background-color: var(--white);
}
.btn-hover-cross {
  overflow: hidden;
  position: relative;
  transition: all 1s;
}
.btn-hover-cross::after {
  position: absolute;
  content: "";
  width: 150%;
  height: 0%;
  left: 50%;
  top: 50%;
  background-color: var(--primary);
  transform: translateX(-50%) translateY(-50%) rotate(0deg);
  transition: all 0.75s;
  opacity: 0.5;
  z-index: -1;
}
.btn-hover-cross:hover {
  border-color: var(--primary);
  background-color: transparent;
}
.btn-hover-cross:hover::after {
  height: 120%;
  opacity: 1;
}
.btn-hover-divide {
  overflow: hidden;
  position: relative;
  transition: all 1s;
  z-index: 1;
}
.btn-hover-divide::after {
  position: absolute;
  content: "";
  width: 150%;
  height: 0%;
  left: 50%;
  top: 50%;
  background-color: var(--primary);
  transform: translateX(-50%) translateY(-50%) rotate(90deg);
  transition: all 0.75s;
  opacity: 0.5;
  z-index: -1;
}
.btn-hover-divide:hover {
  border-color: var(--primary);
  background-color: transparent !important;
  border-color: transparent;
}
.btn-hover-divide:hover::after {
  height: 400%;
  opacity: 1;
}
.btn-hover-cropping {
  overflow: hidden;
  position: relative;
  transition: all 1s;
}
.btn-hover-cropping::after {
  position: absolute;
  content: "";
  width: 150%;
  height: 0%;
  left: 50%;
  top: 50%;
  background-color: var(--primary);
  transform: translateX(-50%) translateY(-50%) rotate(25deg);
  transition: all 0.75s;
  opacity: 0.5;
  z-index: -1;
}
.btn-hover-cropping:hover {
  border-color: var(--primary);
  background-color: transparent;
}
.btn-hover-cropping:hover::after {
  height: 400%;
  opacity: 1;
}
.btn-hover-mask {
  gap: 10px;
  display: inline-flex;
  align-items: center;
  padding: 15px 30px;
  position: relative;
  overflow: hidden;
  transition: all 0.5s;
  border-radius: 5px;
  color: var(--white);
  font-weight: 400;
  font-size: 16px;
  border: 1px solid var(--white);
  z-index: 1;
}
.btn-hover-mask::after {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--black);
  position: absolute;
  content: attr(data-text);
  cursor: pointer;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: var(--white);
  mask: url("https://raw.githubusercontent.com/robin-dela/css-mask-animation/master/img/nature-sprite.webp");
  mask-size: 2300% 100%;
  animation: mask_animation_2 0.7s steps(22) forwards;
}
.btn-hover-mask:hover {
  color: var(--white);
}
.btn-hover-mask:hover::after {
  animation: mask_animation 0.7s steps(22) forwards;
}
.btn-rollover-top {
  position: relative;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
}
.btn-rollover-top:before {
  position: absolute;
  left: 0px;
  bottom: 0px;
  height: 0px;
  width: 100%;
  z-index: -1;
  content: "";
  background-color: var(--primary);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
}
.btn-rollover-top:hover {
  border-color: var(--primary);
  background-color: transparent;
}
.btn-rollover-top:hover::before {
  top: 0%;
  bottom: auto;
  height: 100%;
}
.btn-rollover-left {
  position: relative;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
}
.btn-rollover-left::before {
  position: absolute;
  top: 0px;
  right: 0px;
  height: 100%;
  width: 0px;
  z-index: -1;
  content: "";
  background-color: var(--primary);
  transition: all 0.4s cubic-bezier(0.42, 0, 0.58, 1) 0s;
}
.btn-rollover-left:hover {
  border-color: var(--primary);
  background-color: transparent;
}
.btn-rollover-left:hover::before {
  left: 0%;
  right: auto;
  width: 100%;
}
.btn-rollover-cross {
  overflow: hidden;
  position: relative;
  transition: all 0.5s;
}
.btn-rollover-cross::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  bottom: 100%;
  left: 100%;
  opacity: 0;
  border-bottom: 3px solid var(--primary);
  border-left: 3px solid var(--primary);
  transition: all 0.75s;
}
.btn-rollover-cross::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 100%;
  right: 100%;
  opacity: 0;
  border-top: 3px solid var(--primary);
  border-right: 3px solid var(--primary);
  transition: all 0.75s;
}
.btn-rollover-cross:hover {
  border-color: transparent;
  color: var(--primary);
}
.btn-rollover-cross:hover::before {
  bottom: 0;
  left: 0;
  opacity: 1;
  width: 100%;
  height: 100%;
}
.btn-rollover-cross:hover::after {
  top: 0;
  right: 0;
  opacity: 1;
  width: 100%;
  height: 100%;
}
.btn-parallal-border {
  overflow: hidden;
  position: relative;
  transition: all 0.5s;
}
.btn-parallal-border::before {
  position: absolute;
  content: "";
  width: 0%;
  height: 0%;
  bottom: 0;
  left: 0;
  opacity: 0;
  border-bottom: 3px solid var(--primary);
  border-left: 3px solid var(--primary);
  border-radius: 5px;
  transition: all 0.75s;
}
.btn-parallal-border::after {
  position: absolute;
  content: "";
  width: 0%;
  height: 0%;
  top: 0;
  right: 0;
  opacity: 0;
  border-top: 3px solid var(--primary);
  border-right: 3px solid var(--primary);
  border-radius: 5px;
  transition: all 0.75s;
}
.btn-parallal-border:hover {
  border-color: transparent;
  color: var(--primary);
}
.btn-parallal-border:hover::before {
  opacity: 1;
  width: 100%;
  height: 100%;
}
.btn-parallal-border:hover::after {
  opacity: 1;
  width: 100%;
  height: 100%;
}

/*----------------------------------------*/
/* menu css  */
/*----------------------------------------*/
.main-menu.menu-dark > ul > li > a {
  color: var(--black);
}
.main-menu.menu-light > ul > li > a {
  color: var(--white);
}
.main-menu > ul {
  display: flex;
}
.main-menu > ul > li:hover > a {
  color: var(--primary);
}
.main-menu > ul > li:hover > ul {
  opacity: 1;
  pointer-events: all;
  inset-inline-start: 0;
}
.main-menu > ul > li:hover > ul.dp-menu li:hover > ul {
  opacity: 1;
  pointer-events: all;
  inset-inline-start: 100%;
}
.main-menu li {
  position: relative;
}
.main-menu li a {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 18px;
  line-height: 1;
  color: var(--primary);
  padding: 37px 15px;
  text-transform: capitalize;
}
.main-menu ul.dp-menu {
  background-color: #232529;
  padding: 18px 0px;
  width: 250px;
  position: absolute;
  inset-inline-start: 10px;
  opacity: 0;
  pointer-events: none;
  z-index: 10;
  transition: all 0.5s;
}
.main-menu ul.dp-menu.col-2 {
  column-count: 2;
  width: 480px;
}
.main-menu ul.dp-menu ul {
  background: var(--black);
  padding: 18px 0px;
  width: 300px;
  position: absolute;
  inset-inline-start: calc(100% + 10px);
  top: 0;
  opacity: 0;
  z-index: 10;
  transition: all 0.5s;
}
.main-menu ul.dp-menu li {
  position: relative;
  padding: 0 25px;
}
.main-menu ul.dp-menu li:hover > a {
  color: var(--white);
  background-color: transparent;
}
.main-menu ul.dp-menu li:hover > ul {
  opacity: 1;
  transform: none !important;
  pointer-events: all;
}
.main-menu ul.dp-menu li a {
  font-size: 16px;
  font-weight: 500;
  color: #999999;
  padding: 10px 0;
  background-color: transparent;
  border-radius: 8px;
  text-transform: capitalize;
}
.main-menu ul.dp-menu li a:hover {
  letter-spacing: 0.5px;
}
.main-menu ul.dp-menu li a:after {
  transform: rotate(-90deg);
  margin-left: auto;
}
.main-menu .has-mega-menu {
  position: static;
}
.main-menu li.menu-item-has-children > a:after {
  content: "\f107";
  font-family: var(--font_awesome);
  margin-inline-start: 5px;
  font-weight: 600;
  font-size: 14px;
}
.main-menu .mega-menu {
  background-color: var(--black);
  padding: 30px 50px;
  width: 100%;
  position: absolute;
  left: 10px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 50px;
  justify-content: center;
  overflow: hidden;
  opacity: 0;
  pointer-events: none;
  z-index: 10;
  transition: all 0.5s;
}
@media only screen and (max-width: 1399px) {
  .main-menu .mega-menu {
    column-gap: 30px;
  }
}
.main-menu .mega-menu li:has(ul) > a:after {
  content: "";
}
.main-menu .mega-menu li a {
  font-size: 16px;
  font-weight: 500;
  color: #999999;
  height: 40px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--black);
  border-radius: 8px;
  overflow: hidden;
}
.main-menu .mega-menu li a:hover {
  color: var(--white);
  background: #2C2C2F;
}
.main-menu .mega-menu .title {
  font-weight: 600;
  color: var(--white);
  text-transform: uppercase;
  border-bottom: 1px solid #333337;
  padding-bottom: 20px;
  margin-bottom: 20px;
  pointer-events: none;
  border-radius: 0;
}
.main-menu .mega-style-2 {
  padding: 0 15%;
  gap: 0;
  grid-template-columns: repeat(2, 1fr);
}
.main-menu .mega-style-2 .title {
  height: 70px;
  padding-bottom: 0;
  margin-bottom: 0;
  position: relative;
  overflow: visible;
  padding-left: 30px;
}
.main-menu .mega-style-2 .title:after {
  position: absolute;
  content: "";
  width: 5000px;
  height: 1px;
  background-color: #333337;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
}
.main-menu .mega-style-2 > li:not(:first-child) {
  border-left: 1px solid #333337;
}
.main-menu .mega-style-2 ul {
  column-count: 2;
  position: relative;
  padding: 20px 0;
}
.main-menu .mega-style-2 ul:after {
  position: absolute;
  content: "";
  width: 1px;
  height: 700px;
  background-color: #333337;
  top: 0;
  left: 50%;
  z-index: 1;
}
.main-menu .mega-style-2 ul li a {
  padding-left: 30px;
}
.main-menu .mega-style-3 {
  padding: 0 0 0 20px;
  gap: 0;
  grid-template-columns: repeat(3, 1fr);
}
.main-menu .mega-style-3 .title {
  height: 70px;
  padding-bottom: 0;
  margin-bottom: 0;
  position: relative;
  overflow: visible;
  padding-left: 30px;
}
.main-menu .mega-style-3 .title:after {
  position: absolute;
  content: "";
  width: 5000px;
  height: 1px;
  background-color: #333337;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
}
.main-menu .mega-style-3 > li:not(:first-child) {
  border-left: 1px solid #333337;
}
.main-menu .mega-style-3 > li:last-child {
  border: none;
  width: 36vw;
}
@media only screen and (max-width: 1399px) {
  .main-menu .mega-style-3 > li:last-child {
    width: 32vw;
  }
}
.main-menu .mega-style-3 ul {
  column-count: 2;
  position: relative;
  padding: 20px 0;
  column-gap: 0;
}
.main-menu .mega-style-3 ul:after {
  position: absolute;
  content: "";
  width: 1px;
  height: 700px;
  background-color: #333337;
  top: 0;
  left: 50%;
  z-index: 1;
}
.main-menu .mega-style-3 ul li {
  margin: 0 10px;
}
.main-menu .mega-style-3 ul li a {
  padding-left: 20px;
}
.main-menu .mega-grid-6 {
  grid-template-columns: repeat(6, 1fr);
}
.main-menu .mega-grid-2 {
  grid-template-columns: repeat(2, 1fr);
  row-gap: 60px;
}
.main-menu .list-3-column ul {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 50px;
}
@media only screen and (max-width: 1399px) {
  .main-menu .list-3-column ul {
    column-gap: 30px;
  }
}
.main-menu .span-first-item ul li:first-child {
  grid-column: 1/-1;
  column-span: all;
}
.main-menu .new {
  font-size: 10px;
  font-weight: 600;
  background: #FFA38E;
  color: var(--black);
  padding: 3px 7px;
  line-height: 1;
  border-radius: 2px;
  margin-inline-start: 8px;
  display: inline-block;
}
@media only screen and (max-width: 1199px) {
  .main-menu-2 {
    display: none;
  }
}
.main-menu-2 li {
  display: inline-block;
  padding: 0 10px;
}
.main-menu-2 li a {
  display: block;
  font-weight: 500;
  font-size: 20px;
  line-height: 1.5;
  color: var(--white);
  padding: 10px;
  text-transform: capitalize;
}
.main-menu-2 li a:hover {
  color: var(--primary);
}
@media only screen and (max-width: 1399px) {
  .main-menu-2 li a {
    padding: 5px 0;
  }
}
.main-menu-3 li {
  display: inline-block;
  margin-right: 45px;
}
@media only screen and (max-width: 1199px) {
  .main-menu-3 li {
    margin-right: 25px;
  }
}
.main-menu-3 li:last-child {
  margin-right: 0;
}
.main-menu-3 li a {
  color: var(--white);
  font-weight: 500;
  font-size: 18px;
  line-height: 26px;
}
.main-menu-3 li a:hover {
  color: var(--primary);
}
.main-menu-4 li {
  display: inline-block;
  margin-right: 50px;
}
.main-menu-4 li a {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  color: var(--white);
  text-transform: uppercase;
}
.main-menu-4 li a:hover {
  color: var(--primary);
}

.mega-menu-thumb {
  width: 108%;
  aspect-ratio: 100/83;
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
}
@media only screen and (max-width: 1199px) {
  .mega-menu-thumb {
    width: 100%;
    height: 100%;
  }
}
.mega-menu-thumb:after {
  position: absolute;
  content: "";
  width: 76%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(270deg, rgba(28, 29, 32, 0) 0%, #1C1D20 100%);
}
.mega-menu-thumb .laptop-view {
  width: 70%;
  aspect-ratio: 100/114;
  object-fit: cover;
  object-position: center top;
  position: absolute;
  right: 70px;
  bottom: 0;
}

.mega-menu-counter__item {
  text-align: center;
  display: inline-block;
  margin-top: 35%;
  margin-left: 17%;
  position: relative;
}
@media only screen and (max-width: 1199px) {
  .mega-menu-counter__item {
    margin: 30px auto 50px;
  }
}
.mega-menu-counter__text p {
  font-size: 30px;
  line-height: 28px;
  color: var(--white);
  font-weight: 500;
}
.mega-menu-counter__number {
  font-size: 150px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 24px;
  color: var(--white);
  background: linear-gradient(136deg, #9479FF 0%, #FFA6D6 47.92%, #FFFCE3 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media only screen and (max-width: 1199px) {
  .menu-with-number {
    display: none;
  }
}
.menu-with-number li {
  display: inline-block;
}
.menu-with-number li a {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5;
  color: var(--white);
  padding: 34px 40px;
  display: inline-block;
  text-transform: uppercase;
}
.menu-with-number li a:hover span {
  color: var(--white);
}
.menu-with-number li a:hover span::before {
  background-color: var(--white);
}
.menu-with-number li a.active span {
  color: var(--white);
}
.menu-with-number li a.active span::before {
  position: absolute;
  content: "";
  width: 35px;
  height: 1px;
  right: 20px;
  top: 50%;
  background-color: var(--white);
}
.menu-with-number li a span {
  display: block;
  font-weight: 500;
  font-size: 12px;
  line-height: 10px;
  text-align: right;
  color: #999999;
  position: relative;
  transition: all 0.5s;
}
.menu-with-number li a span::before {
  position: absolute;
  content: "";
  width: 35px;
  height: 1px;
  right: 20px;
  top: 50%;
  transition: all 0.5s;
  background-color: var(--black-6);
}

.sidebar-menu li {
  display: block;
  padding-bottom: 15px;
}
@media only screen and (max-width: 1919px) {
  .sidebar-menu li {
    padding-bottom: 10px;
  }
}
.sidebar-menu li a {
  display: block;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.5;
  color: var(--white);
  padding: 10px 0;
  text-transform: uppercase;
}
.sidebar-menu li a:hover, .sidebar-menu li a.active {
  color: var(--primary);
}
@media only screen and (max-width: 1399px) {
  .sidebar-menu li a {
    padding: 5px 0;
  }
}

/* mean menu customize */
.offcanvas__menu-wrapper.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}
.offcanvas__menu-wrapper.mean-container .mean-nav > ul > li:last-child > a {
  border-bottom: 1px solid var(--black-4);
}
.offcanvas__menu-wrapper.mean-container .mean-nav {
  background: none;
  margin-top: 0;
}
.offcanvas__menu-wrapper.mean-container .mean-nav .new {
  font-size: 10px;
  font-weight: 600;
  background: #FFA38E;
  color: var(--black);
  padding: 3px 7px;
  line-height: 1;
  display: flex;
  align-items: center;
  border-radius: 2px;
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li a {
  width: 100%;
  padding: 15px 0;
  padding-inline-start: 15px;
  font-weight: 400;
  font-size: 22px;
  line-height: 1;
  color: var(--white);
  text-transform: capitalize;
  border-top: 1px solid var(--black-4);
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  align-items: center;
  outline: none;
  transform: translateY(var(--y)) translateZ(0);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  box-sizing: border-box;
}
@media only screen and (max-width: 767px) {
  .offcanvas__menu-wrapper.mean-container .mean-nav ul li a {
    font-size: 20px;
  }
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li a.mean-expand {
  width: 54px;
  height: 54px;
  justify-content: center;
  font-weight: 300;
  border: none !important;
}
@media only screen and (max-width: 767px) {
  .offcanvas__menu-wrapper.mean-container .mean-nav ul li a.mean-expand {
    height: 50px;
  }
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li a.mean-expand:hover {
  background: var(--secondary);
  opacity: 1;
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li li:first-child {
  border-top: 1px solid var(--black-4);
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li li a {
  font-size: 20px;
  text-transform: capitalize;
  border-top: none !important;
  padding: 12px 0;
  padding-inline-start: 30px;
}
@media only screen and (max-width: 767px) {
  .offcanvas__menu-wrapper.mean-container .mean-nav ul li li a {
    font-size: 18px;
  }
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li li a.mean-expand {
  height: 58px;
}
@media only screen and (max-width: 991px) {
  .offcanvas__menu-wrapper.mean-container .mean-nav ul li li a.mean-expand {
    height: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .offcanvas__menu-wrapper.mean-container .mean-nav ul li li a.mean-expand {
    height: 22px;
  }
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li li li:last-child {
  border-bottom: 1px solid var(--black-4);
}
.offcanvas__menu-wrapper.mean-container .mean-nav ul li li li a {
  padding-left: 40px;
}
.offcanvas__menu-wrapper.mean-container .mean-bar {
  padding: 0;
  background: none;
  max-height: auto;
  overflow-y: scroll;
}
.offcanvas__menu-wrapper.mean-container .mean-bar::-webkit-scrollbar {
  width: 0;
}
.offcanvas__menu-wrapper.mean-container a.meanmenu-reveal {
  display: none !important;
}

.light .main-menu li a:hover {
  color: var(--primary);
}
.light .main-menu-2 li a {
  color: var(--black);
}
.light .main-menu-2 li a:hover {
  color: var(--primary);
}
.light .main-menu-3 li a {
  color: var(--black);
}
.light .main-menu-3 li a:hover {
  color: var(--primary);
}
.light .sidebar-menu li a {
  color: var(--black);
}
.light .sidebar-menu li a:hover {
  color: var(--primary);
}
.light .menu-with-number li a {
  color: var(--black);
}
.light .menu-with-number li a:hover span {
  color: var(--black);
}
.light .menu-with-number li a:hover span::before {
  background-color: var(--black);
}
.light .menu-with-number li a span {
  color: var(--black-9);
}
.light .menu-with-number li a span::before {
  background-color: var(--black-9);
}
.light .menu-with-number li a.active span {
  color: var(--black);
}
.light .menu-with-number li a.active span::before {
  background-color: var(--black);
}

/*----------------------------------------*/
/* modal css  */
/*----------------------------------------*/
.modal__dialog {
  width: 760px;
  max-width: 100%;
  margin-top: 100px;
}
@media only screen and (max-width: 991px) {
  .modal__dialog {
    width: 700px;
    margin-top: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .modal__dialog {
    width: 350px;
  }
}
.modal__content {
  height: 500px;
}
@media only screen and (max-width: 767px) {
  .modal__content {
    height: 300px;
  }
}
.modal__content iframe {
  width: 100%;
  height: 100%;
}
.modal__close {
  position: absolute;
  width: 40px;
  height: 40px;
  top: -15px;
  right: -15px;
  z-index: 9;
  border-radius: 50px;
  font-size: 20px;
  color: var(--white);
  background: var(--black);
  transition: all 0.3s;
}
.modal__close:hover {
  color: var(--primary);
}
.modal__sfluence {
  width: 100%;
  height: 100%;
  padding: 60px;
}
@media only screen and (max-width: 767px) {
  .modal__sfluence {
    padding: 20px 10px;
  }
}
.modal__sfluence-area {
  width: 100vw;
  height: 100vh;
  background: var(--black);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s;
  transform: scale(0.5);
}
.modal__sfluence-area.showed {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}
.modal__sfluence-area .close_btn {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 0px;
  right: 20px;
  z-index: 9;
  border-radius: 50px;
  font-size: 30px;
  color: var(--white);
  transition: all 0.3s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal__sfluence-area .close_btn:hover {
  color: var(--primary);
}
@media only screen and (max-width: 767px) {
  .modal__sfluence-area .close_btn {
    right: 0;
  }
}
.modal__sfluence-area iframe,
.modal__sfluence-area video {
  width: 100%;
  height: 100%;
}
@media only screen and (max-width: 767px) {
  .modal__sfluence-area iframe,
  .modal__sfluence-area video {
    height: 300px;
    object-fit: cover;
    margin-top: 45%;
  }
}

/*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */
/* cursor css  */
.cb-cursor {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 150;
  contain: layout style size;
  pointer-events: none;
  will-change: transform;
  -webkit-transition: opacity 0.3s, color 0.4s;
  -o-transition: opacity 0.3s, color 0.4s;
  -moz-transition: opacity 0.3s, color 0.4s;
  transition: opacity 0.3s, color 0.4s;
}

.cb-cursor:before {
  content: "";
  position: absolute;
  top: -24px;
  left: -24px;
  display: block;
  width: 80px;
  height: 30px;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  border-radius: 25px;
  -webkit-transition: opacity 0.1s, -webkit-transform 0.3s ease-in-out;
  transition: opacity 0.1s, -webkit-transform 0.3s ease-in-out;
  -o-transition: opacity 0.1s, -o-transform 0.3s ease-in-out;
  -moz-transition: transform 0.3s ease-in-out, opacity 0.1s, -moz-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, opacity 0.1s;
  transition: transform 0.3s ease-in-out, opacity 0.1s, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out, -o-transform 0.3s ease-in-out;
}

.cb-cursor-text {
  position: absolute;
  top: -28px;
  left: -4px;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transform: scale(0) rotate(10deg);
  -moz-transform: scale(0) rotate(10deg);
  -ms-transform: scale(0) rotate(10deg);
  -o-transform: scale(0) rotate(10deg);
  transform: scale(0) rotate(10deg);
  opacity: 0;
  color: black;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  white-space: nowrap;
  letter-spacing: -0.01em;
  -webkit-transition: opacity 0.4s, -webkit-transform 0.3s;
  transition: opacity 0.4s, -webkit-transform 0.3s;
  -o-transition: opacity 0.4s, -o-transform 0.3s;
  -moz-transition: opacity 0.4s, transform 0.3s, -moz-transform 0.3s;
  transition: opacity 0.4s, transform 0.3s;
  transition: opacity 0.4s, transform 0.3s, -webkit-transform 0.3s, -moz-transform 0.3s, -o-transform 0.3s;
}

@supports (mix-blend-mode: exclusion) {
  .cb-cursor.-exclusion,
  .cb-cursor.-opaque {
    mix-blend-mode: exclusion;
  }
}
@supports (mix-blend-mode: exclusion) {
  .cb-cursor.-exclusion:before,
  .cb-cursor.-opaque:before {
    background: white;
  }
}
.cb-cursor.-normal,
.cb-cursor.-text {
  mix-blend-mode: normal;
}

.cb-cursor.-normal:before,
.cb-cursor.-text:before {
  background: currentColor;
}

.cb-cursor.-inverse {
  color: white;
}

.cb-cursor.-visible:before {
  -webkit-transform: scale(0.2);
  -moz-transform: scale(0.2);
  -ms-transform: scale(0.2);
  -o-transform: scale(0.2);
  transform: scale(0.2);
}

.cb-cursor.-visible.-active:before {
  -webkit-transform: scale(0.23);
  -moz-transform: scale(0.23);
  -ms-transform: scale(0.23);
  -o-transform: scale(0.23);
  transform: scale(0.23);
  -webkit-transition-duration: 0.2s;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  transition-duration: 0.2s;
}

.cb-cursor.-pointer:before {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.cb-cursor.-text:before {
  background: #fff;
  -webkit-transform: scale(1.7);
  -moz-transform: scale(1.7);
  -ms-transform: scale(1.7);
  -o-transform: scale(1.7);
  transform: scale(1.7);
}

.cb-cursor.-text .cb-cursor-text {
  opacity: 1;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.cb-cursor.-text.-active:before {
  -webkit-transform: scale(1.6);
  -moz-transform: scale(1.6);
  -ms-transform: scale(1.6);
  -o-transform: scale(1.6);
  transform: scale(1.6);
  -webkit-transition-duration: 0.2s;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  transition-duration: 0.2s;
}

.cb-cursor.-opaque:before {
  -webkit-transform: scale(1.32);
  -moz-transform: scale(1.32);
  -ms-transform: scale(1.32);
  -o-transform: scale(1.32);
  transform: scale(1.32);
}

.cb-cursor.-opaque.-active:before {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.cb-cursor.-lg:before {
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
}

.cb-cursor.-hidden:before {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.-color-red {
  color: red;
}

.-color-green {
  color: #51c67d;
}

.cb-demo {
  background: #fff;
}

.cb-demo-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100vh;
}

.cb-demo-container {
  padding: 0 20px;
}

@media (min-width: 1600px) {
  .cb-demo-container {
    padding: 0 120px;
  }
}
.cb-demo-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  margin: 30px 0;
}

@media (min-width: 1600px) {
  .cb-demo-row {
    margin: 60px 0;
  }
}
.cb-demo-item {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 50px 30px;
  margin: 0 20px;
  color: #000;
}

@media (min-width: 1600px) {
  .cb-demo-item {
    padding: 90px 30px;
    margin: 0 30px;
  }
}
.cb-demo-item-title {
  position: relative;
  margin: 0 0 25px 0;
  font-size: 30px;
  font-weight: bold;
}

.cb-demo-item-text {
  position: relative;
  max-width: 70%;
  margin: 0 auto;
  color: rgba(0, 0, 0, 0.5);
  font-size: 16px;
  font-weight: 300;
  line-height: 150%;
}

.cb-demo-item:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  background: #f8f8f8;
  -webkit-transition: -webkit-box-shadow 0.2s;
  transition: -webkit-box-shadow 0.2s;
  -o-transition: box-shadow 0.2s;
  -moz-transition: box-shadow 0.2s, -moz-box-shadow 0.2s;
  transition: box-shadow 0.2s;
  transition: box-shadow 0.2s, -webkit-box-shadow 0.2s, -moz-box-shadow 0.2s;
}

.cb-cursor.-green {
  color: green;
}

.cb-cursor.-green:before {
  background: green;
}

.cb-cursor.-red .cb-cursor-text {
  color: black;
  top: 0px;
  left: 0px;
  width: 50px;
  height: 50px;
  text-transform: uppercase;
}

.cb-cursor.-red {
  color: black;
  background-color: black;
  width: 5px;
  height: 5px;
}

.cb-cursor.-red:before {
  border-radius: 500px;
  background: white;
  width: 100px;
  height: 100px;
  color: #fff;
}

.cb-cursor.-portfolio:before {
  display: none;
}
.cb-cursor.-portfolio .cb-cursor-text {
  top: 0;
  left: 0;
  width: auto;
  height: auto;
  display: block;
  text-align: start;
}
.cb-cursor.-portfolio .content {
  position: absolute;
  top: 0px;
  left: 0px;
}
.cb-cursor.-portfolio .title {
  font-weight: 300;
  font-size: 30px;
  line-height: 27px;
  background-color: var(--white);
  padding: 15px 20px 13px;
  color: var(--black);
}
.cb-cursor.-portfolio .meta {
  font-family: var(--font_tartuffotrial);
  font-weight: 300;
  font-style: italic;
  font-size: 16px;
  line-height: 27px;
  background-color: var(--white);
  display: inline-block;
  padding: 5px 15px 3px;
  margin-top: 3px;
  color: var(--black);
}

/* digital agency page css */
.body-digital-agency {
  background-color: #F8F8F8;
  color: var(--secondary);
}
.body-digital-agency.dark {
  background-color: #111111;
}
@media (min-width: 1800px) {
  .body-digital-agency .container.large {
    max-width: 1750px;
  }
}
.body-digital-agency .section-header {
  margin-top: 20px;
}
.body-digital-agency .section-subtitle {
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  display: inline-block;
  text-transform: uppercase;
  color: var(--primary);
  letter-spacing: 1px;
}

/* hero area style  */
.hero-area {
  background-color: rgba(255, 129, 58, 0.15);
  position: relative;
  z-index: 2;
}
.dark .hero-area {
  background-color: #171717;
}
.hero-area .hero-content {
  display: grid;
  gap: 40px 100px;
  grid-template-columns: 130px 1fr 560px;
  margin-top: 61px;
  margin-bottom: 220px;
}
@media only screen and (max-width: 1919px) {
  .hero-area .hero-content {
    grid-template-columns: 130px 1fr 490px;
    margin-bottom: 140px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area .hero-content {
    grid-template-columns: 130px 1fr 460px;
    gap: 40px 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area .hero-content {
    grid-template-columns: 130px 1fr;
    margin-bottom: 0px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area .hero-content {
    margin-top: 31px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area .hero-content {
    grid-template-columns: 1fr;
  }
}
.hero-area .section-title {
  font-size: 100px;
  font-weight: 500;
  line-height: 0.9;
  max-width: 660px;
}
@media only screen and (max-width: 1919px) {
  .hero-area .section-title {
    font-size: 70px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area .section-title {
    font-size: 50px;
    max-width: 340px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area .section-title {
    max-width: 590px;
  }
}
.hero-area .section-title .title-shape-1 {
  margin-left: 10px;
  margin-right: 10px;
  margin-top: -8px;
  width: 100px;
  display: inline-flex;
}
@media only screen and (max-width: 1919px) {
  .hero-area .section-title .title-shape-1 {
    width: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area .section-title .title-shape-1 {
    width: 55px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area .section-title .title-shape-1 {
    width: 45px;
    margin-left: 5px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
.hero-area .feature-box .number {
  font-size: 100px;
  font-weight: 400;
  line-height: 0.72;
  display: inline-block;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .hero-area .feature-box .number {
    font-size: 70px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area .feature-box .number {
    font-size: 50px;
  }
}
.hero-area .feature-box .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 21px;
  color: var(--primary);
  margin-top: 21px;
}
.hero-area .text-wrapper {
  margin-top: 87px;
}
@media only screen and (max-width: 1919px) {
  .hero-area .text-wrapper {
    margin-top: 37px;
  }
}
.hero-area .text-wrapper .text {
  font-size: 22px;
  font-weight: 400;
  line-height: 30px;
  color: var(--primary);
  max-width: 490px;
}
@media only screen and (max-width: 1399px) {
  .hero-area .text-wrapper .text {
    font-size: 18px;
  }
}
.hero-area .award-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 1;
}
@media only screen and (max-width: 1199px) {
  .hero-area .award-wrapper {
    grid-row: span 2;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area .award-wrapper {
    order: 3;
  }
}
.hero-area .award-wrapper:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--border);
  top: 0;
  left: calc(50% - 2px);
  z-index: -1;
}
@media only screen and (max-width: 767px) {
  .hero-area .award-wrapper:before {
    display: none;
  }
}
.hero-area .award-wrapper:after {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--border);
  top: 0;
  left: calc(50% + 1px);
  z-index: -1;
}
@media only screen and (max-width: 767px) {
  .hero-area .award-wrapper:after {
    display: none;
  }
}
.hero-area .award-wrapper .circle-text-wrapper {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #F9E6DC;
}
.dark .hero-area .award-wrapper .circle-text-wrapper {
  background-color: var(--bg);
}
.hero-area .award-wrapper .circle-text {
  width: 130px;
  height: 130px;
  padding: 0;
}
.hero-area .features-wrapper-box {
  padding-bottom: 36px;
  border-bottom: 1px solid var(--border);
}
.hero-area .features-wrapper {
  display: grid;
  gap: 40px 60px;
  grid-template-columns: repeat(2, 230px);
  justify-content: space-between;
}
@media only screen and (max-width: 1919px) {
  .hero-area .features-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .hero-area .features-wrapper {
    grid-template-columns: repeat(1, 100%);
  }
}
.hero-area .section-content {
  margin-top: 10px;
}
@media only screen and (max-width: 1199px) {
  .hero-area .section-content {
    max-width: 600px;
  }
}
.hero-area .big-text {
  font-family: var(--font_thunder);
  font-size: 920px;
  font-weight: 700;
  line-height: 0.477;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  color: var(--theme);
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (max-width: 1919px) {
  .hero-area .big-text {
    font-size: 690px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area .big-text {
    font-size: 590px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area .big-text {
    display: none;
  }
}

video.video-area {
  width: 100%;
}

/* about area style  */
.about-area {
  position: relative;
}
.about-area .pin-spacer {
  z-index: 1;
}
.about-area-inner {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.about-area .section-title {
  font-family: var(--font_thunder);
  font-size: 120px;
  font-weight: 700;
  line-height: 0.83;
  text-transform: uppercase;
  display: inline;
  position: relative;
}
@media only screen and (max-width: 1399px) {
  .about-area .section-title {
    font-size: 80px;
  }
}
.about-area .section-content {
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.about-area .section-content .shape-1 {
  position: absolute;
  content: "";
  width: 20px;
  height: 20px;
  border-left: 1px solid var(--primary);
  border-top: 1px solid var(--primary);
  top: 0;
  left: 0;
}
.about-area .section-content .shape-2 {
  position: absolute;
  content: "";
  width: 20px;
  height: 20px;
  border-right: 1px solid var(--primary);
  border-top: 1px solid var(--primary);
  top: 0;
  right: 0;
}
.about-area .section-content .shape-3 {
  position: absolute;
  content: "";
  width: 20px;
  height: 20px;
  border-left: 1px solid var(--primary);
  border-bottom: 1px solid var(--primary);
  bottom: 0;
  left: 0;
}
.about-area .section-content .shape-4 {
  position: absolute;
  content: "";
  width: 20px;
  height: 20px;
  border-right: 1px solid var(--primary);
  border-bottom: 1px solid var(--primary);
  bottom: 0;
  right: 0;
}
.about-area .section-content .text {
  font-size: 30px;
  font-weight: 400;
  line-height: 1.26;
  letter-spacing: -0.02em;
  max-width: 950px;
  color: var(--primary);
  margin-inline: auto;
}
@media only screen and (max-width: 1399px) {
  .about-area .section-content .text {
    font-size: 24px;
    max-width: 800px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area .section-content .text {
    font-size: 20px;
  }
}
.about-area .section-content .text-wrapper {
  opacity: 0;
  transform: translateY(100px);
  transition: transform 0.5s, opacity 0.5s;
}
@media only screen and (max-width: 1199px) {
  .about-area .section-content .text-wrapper {
    opacity: 1;
    transform: translateY(0);
  }
}
.about-area .section-content .btn-wrapper {
  margin-top: 62px;
  opacity: 0;
  transform: translateY(100px);
  transition: transform 0.5s, opacity 0.5s;
}
@media only screen and (max-width: 1199px) {
  .about-area .section-content .btn-wrapper {
    opacity: 1;
    margin-top: 50px;
    transform: translateY(0);
  }
}
.about-area .section-title-wrapper {
  display: none;
}
@media only screen and (max-width: 1199px) {
  .about-area .section-title-wrapper {
    display: block;
    margin-bottom: 20px;
  }
}

/* work area style  */
.work-area-inner {
  margin-top: 18px;
}
.work-area .section-header {
  display: grid;
  gap: 15px 20px;
  grid-template-columns: 1fr 2fr 1fr;
}
@media only screen and (max-width: 767px) {
  .work-area .section-header {
    grid-template-columns: 1fr;
  }
}
.work-area .section-header .text-wrapper {
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .work-area .section-header .text-wrapper {
    text-align: start;
  }
}
.work-area .section-header .text-wrapper .text {
  font-family: var(--font_dmsans);
  font-size: 18px;
  font-style: italic;
  font-weight: 400;
  line-height: 18px;
  color: var(--primary);
}
.work-area .section-header .total-count {
  text-align: end;
}
@media only screen and (max-width: 767px) {
  .work-area .section-header .total-count {
    text-align: start;
  }
}
.work-area .section-header .total-count .number {
  display: inline-block;
  font-family: var(--font_dmsans);
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  color: var(--primary);
}
.work-area .section-title {
  font-family: var(--font_dmsans);
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
}
.work-area .works-wrapper-box {
  margin-top: 67px;
}
@media only screen and (max-width: 1199px) {
  .work-area .works-wrapper-box {
    margin-top: 47px;
  }
}
.work-area .all-btn-wrapper {
  margin-top: 70px;
  text-align: center;
}
@media only screen and (max-width: 1199px) {
  .work-area .all-btn-wrapper {
    margin-top: 50px;
  }
}

.works-wrapper-1 {
  display: grid;
  gap: 60px 40px;
  grid-template-columns: repeat(2, 1fr);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-1 {
    gap: 40px 30px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-1 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.works-wrapper-1 > * .image {
  transform-origin: bottom right;
}
.works-wrapper-1 > *:nth-child(2n) .image {
  transform-origin: bottom left;
}
.works-wrapper-1 .work-box .thumb:hover .t-btn {
  opacity: 1;
}
.works-wrapper-1 .work-box .thumb .image {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  transform: scale(0.9);
}
.works-wrapper-1 .work-box .thumb .image img {
  transform-origin: center;
}
.works-wrapper-1 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-1 .work-box .thumb .t-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  padding: 10px 20px;
  display: inline-block;
  background-color: white;
  color: var(--black);
  border-radius: 50px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  margin: -25px 0 0 -65px;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  pointer-events: none;
}
.works-wrapper-1 .work-box .content {
  margin-top: 13px;
}
.works-wrapper-1 .work-box .title {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.25;
  letter-spacing: -0.02em;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-1 .work-box .title {
    font-size: 20px;
  }
}
.works-wrapper-1 .work-box .meta {
  display: flex;
  gap: 8px;
  align-items: center;
}
.works-wrapper-1 .work-box .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  color: #999999;
  display: flex;
  align-items: center;
}
.works-wrapper-1 .work-box .meta span:not(:first-child):before {
  content: "";
  width: 4px;
  height: 4px;
  background-color: var(--primary);
  display: inline-block;
  border-radius: 50%;
  margin-inline-end: 4px;
}

/* service area style  */
.service-area .services-wrapper-box {
  margin-top: 91px;
  margin-bottom: 20px;
}
@media only screen and (max-width: 1919px) {
  .service-area .services-wrapper-box {
    margin-top: 71px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area .services-wrapper-box {
    margin-top: 61px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-area .services-wrapper-box {
    margin-top: 41px;
  }
}

.services-wrapper-1 .service-box {
  border-top: 1px solid var(--border);
  padding-top: 30px;
  padding-bottom: 30px;
  display: grid;
  gap: 20px 30px;
  grid-template-columns: 1fr 410px 545px;
  align-items: flex-start;
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-1 .service-box {
    grid-template-columns: 1fr 310px 445px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-1 .service-box {
    grid-template-columns: 1fr 220px 360px;
  }
}
@media only screen and (max-width: 767px) {
  .services-wrapper-1 .service-box {
    grid-template-columns: 1fr 380px;
  }
}
@media (max-width: 575px) {
  .services-wrapper-1 .service-box {
    grid-template-columns: 1fr;
  }
}
.services-wrapper-1 .service-box .count .number {
  font-size: 30px;
  font-weight: 500;
  line-height: 1;
  color: var(--primary);
  display: inline-block;
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-1 .service-box .count .number {
    font-size: 25px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-1 .service-box .count .number {
    font-size: 20px;
  }
}
.services-wrapper-1 .service-box .content .title {
  font-size: 30px;
  font-weight: 500;
  line-height: 1;
  color: var(--primary);
}
@media only screen and (max-width: 991px) {
  .services-wrapper-1 .service-box .content .title {
    font-size: 24px;
  }
}
.services-wrapper-1 .service-box .content .title a:hover {
  color: var(--secondary);
}
.services-wrapper-1 .service-box .service-list {
  margin-top: 22px;
}
.services-wrapper-1 .service-box .service-list li {
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
  color: var(--primary);
}
.services-wrapper-1 .service-box .service-list li a:hover {
  color: var(--secondary);
}
.services-wrapper-1 .service-box .thumb {
  border-radius: 20px;
  overflow: hidden;
  text-align: right;
}
@media only screen and (max-width: 991px) {
  .services-wrapper-1 .service-box .thumb {
    border-radius: 10px;
  }
}
@media only screen and (max-width: 767px) {
  .services-wrapper-1 .service-box .thumb {
    grid-column: span 2;
  }
}
@media (max-width: 575px) {
  .services-wrapper-1 .service-box .thumb {
    grid-column: auto;
  }
}
.services-wrapper-1 .service-box .thumb img {
  width: 35%;
  object-fit: cover;
  height: 265px;
  border-radius: 20px;
}
@media only screen and (max-width: 991px) {
  .services-wrapper-1 .service-box .thumb img {
    width: 100%;
    border-radius: 10px;
  }
}

/* funfact area style  */
.funfact-area {
  background-color: var(--bg);
}
.funfact-area .section-title {
  color: var(--white);
}
.funfact-area .section-title-wrapper {
  margin-top: 41px;
}
.funfact-area .funfact-area-inner {
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 1fr 950px;
}
@media only screen and (max-width: 1919px) {
  .funfact-area .funfact-area-inner {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 1399px) {
  .funfact-area .funfact-area-inner {
    grid-template-columns: 1fr 650px;
  }
}
@media only screen and (max-width: 1199px) {
  .funfact-area .funfact-area-inner {
    grid-template-columns: 1fr 550px;
  }
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-area-inner {
    grid-template-columns: 1fr;
  }
}
.funfact-area .funfact-wrapper-box {
  padding-left: 30px;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-wrapper-box {
    padding-left: 0;
    padding-top: 0;
  }
}
.funfact-area .funfact-wrapper-box .line-1 {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.08);
  top: 0;
  left: 0;
  z-index: -1;
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-wrapper-box .line-1 {
    display: none;
  }
}
.funfact-area .funfact-wrapper-box .line-2 {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.08);
  top: 0;
  left: 30px;
  z-index: -1;
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-wrapper-box .line-2 {
    display: none;
  }
}
.funfact-area .funfact-wrapper-box .line-3 {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.08);
  top: 0;
  right: 390px;
  z-index: -1;
}
@media only screen and (max-width: 1199px) {
  .funfact-area .funfact-wrapper-box .line-3 {
    right: 320px;
  }
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-wrapper-box .line-3 {
    display: none;
  }
}
.funfact-area .funfact-wrapper-box .line-4 {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.08);
  top: 0;
  right: 360px;
  z-index: -1;
}
@media only screen and (max-width: 1199px) {
  .funfact-area .funfact-wrapper-box .line-4 {
    right: 290px;
  }
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-wrapper-box .line-4 {
    display: none;
  }
}
.funfact-area .funfact-wrapper {
  margin-top: 52px;
  margin-bottom: 43px;
}
.funfact-area .funfact-item {
  max-width: 360px;
  opacity: 0.2;
}
@media only screen and (max-width: 1199px) {
  .funfact-area .funfact-item {
    max-width: 290px;
  }
}
.funfact-area .funfact-item:not(:first-child) {
  margin-top: 195px;
}
@media only screen and (max-width: 1399px) {
  .funfact-area .funfact-item:not(:first-child) {
    margin-top: 155px;
  }
}
@media only screen and (max-width: 1199px) {
  .funfact-area .funfact-item:not(:first-child) {
    margin-top: 115px;
  }
}
@media only screen and (max-width: 991px) {
  .funfact-area .funfact-item:not(:first-child) {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .funfact-area .funfact-item:not(:first-child) {
    margin-top: 50px;
  }
}
.funfact-area .funfact-item:nth-child(2n) {
  margin-left: auto;
}
@media (max-width: 575px) {
  .funfact-area .funfact-item:nth-child(2n) {
    margin-left: 0;
  }
}
.funfact-area .funfact-item .number {
  font-size: 130px;
  font-weight: 500;
  line-height: 0.7;
  display: inline-block;
  color: var(--white);
}
@media only screen and (max-width: 1399px) {
  .funfact-area .funfact-item .number {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .funfact-area .funfact-item .number {
    font-size: 60px;
  }
}
.funfact-area .funfact-item .text {
  margin-top: 34px;
  color: #999999;
}
@media only screen and (max-width: 1399px) {
  .funfact-area .funfact-item .text {
    margin-top: 24px;
  }
}

/* client area style  */
.client-area .section-title {
  max-width: 1430px;
}
.client-area .section-title span {
  color: var(--theme);
}
.client-area .section-content {
  margin-top: 21px;
}
.client-area .section-content .text-wrapper {
  max-width: 505px;
  margin-top: 133px;
  margin-left: 545px;
}
@media only screen and (max-width: 1919px) {
  .client-area .section-content .text-wrapper {
    margin-top: 83px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area .section-content .text-wrapper {
    margin-top: 63px;
    margin-left: 345px;
  }
}
@media only screen and (max-width: 991px) {
  .client-area .section-content .text-wrapper {
    margin-top: 43px;
    margin-left: auto;
  }
}
@media only screen and (max-width: 767px) {
  .client-area .section-content .text-wrapper {
    max-width: 100%;
    margin-top: 23px;
  }
}
.client-area .client-capsule-wrapper {
  position: relative;
  overflow: hidden;
  pointer-events: none;
  margin-top: -200px;
  height: 633px;
}
@media only screen and (max-width: 1919px) {
  .client-area .client-capsule-wrapper {
    height: 533px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area .client-capsule-wrapper {
    height: 483px;
  }
}
@media only screen and (max-width: 991px) {
  .client-area .client-capsule-wrapper {
    height: 433px;
  }
}
.client-area .client-capsule-wrapper > * {
  position: absolute;
  display: inline-block;
  margin-bottom: 0;
  left: 0;
  top: 0;
  user-select: none;
  pointer-events: auto;
  transition: none;
}
.client-area .client-box {
  width: 215px;
  height: 100px;
  padding: 10px 20px;
  background-color: var(--primary);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  transform: translate(-50%, -50%) rotate(0rad);
}
@media only screen and (max-width: 1919px) {
  .client-area .client-box {
    width: 165px;
    height: 70px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area .client-box {
    width: 135px;
    height: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .client-area .client-box {
    width: 105px;
    height: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .client-area .client-box {
    width: 85px;
    height: 30px;
  }
}
.client-area .client-box img {
  pointer-events: none;
  max-width: 100%;
  max-height: 100%;
}
.client-area .line {
  border-bottom: 1px solid var(--primary);
}
.client-area .lines-wrapper {
  display: grid;
  gap: 5px 0;
}
@media only screen and (max-width: 1399px) {
  .client-area .lines-wrapper {
    gap: 3px 0;
  }
}
@media only screen and (max-width: 767px) {
  .client-area .lines-wrapper {
    gap: 1px 0;
  }
}

/* cta area style  */
.cta-area {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}
.cta-area-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}
.cta-area-inner .area-bg {
  position: absolute;
  height: 390px;
  width: 390px;
  background-color: var(--black);
  border-radius: 100%;
  z-index: -1;
}
.dark .cta-area-inner .area-bg {
  background-color: var(--theme);
}
@media only screen and (max-width: 1919px) {
  .cta-area-inner .area-bg {
    height: 300px;
    width: 300px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-inner .area-bg {
    height: 260px;
    width: 260px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-inner .area-bg {
    height: 200px;
    width: 200px;
  }
}
@media only screen and (max-width: 991px) {
  .cta-area-inner .area-bg {
    height: 160px;
    width: 160px;
  }
}
.cta-area .section-title {
  font-family: var(--font_thunder);
  font-size: 360px;
  font-weight: 700;
  line-height: 20px;
  text-transform: uppercase;
  color: var(--white);
  display: inline-block;
  font-size: 4vw;
}
.cta-area .section-title a:hover {
  color: var(--white);
}
.cta-area .section-content {
  text-align: center;
}

/* productivity area style  */
.productivity-area .section-content {
  margin-top: 148px;
  margin-bottom: 105px;
  text-align: center;
}
@media only screen and (max-width: 1919px) {
  .productivity-area .section-content {
    margin-top: 108px;
    margin-bottom: 75px;
  }
}
@media only screen and (max-width: 991px) {
  .productivity-area .section-content {
    margin-top: 58px;
    margin-bottom: 55px;
  }
}
.productivity-area .section-title {
  max-width: 1140px;
  margin-inline: auto;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 1919px) {
  .productivity-area .section-title {
    max-width: 937px;
  }
}
@media only screen and (max-width: 1399px) {
  .productivity-area .section-title {
    max-width: 737px;
  }
}
@media only screen and (max-width: 1199px) {
  .productivity-area .section-title {
    max-width: 637px;
  }
}
@media only screen and (max-width: 991px) {
  .productivity-area .section-title {
    max-width: 527px;
  }
}
.productivity-area .section-title span {
  color: #999999;
  display: inline-block;
}
.dark .productivity-area .section-title span {
  color: #555555;
}
.productivity-area .section-title .shape-1:hover:before {
  opacity: 1;
  visibility: visible;
}
.productivity-area .section-title .shape-1:before {
  content: "";
  width: 150px;
  aspect-ratio: 100/100;
  background-image: url(../imgs/shape/shape-5.webp);
  background-size: contain;
  position: absolute;
  top: -141px;
  right: 144px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.1s;
  display: inline-block;
}
@media only screen and (max-width: 1919px) {
  .productivity-area .section-title .shape-1:before {
    width: 100px;
  }
}
@media only screen and (max-width: 1399px) {
  .productivity-area .section-title .shape-1:before {
    width: 90px;
  }
}
@media only screen and (max-width: 1199px) {
  .productivity-area .section-title .shape-1:before {
    width: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .productivity-area .section-title .shape-1:before {
    top: -91px;
  }
}
.productivity-area .section-title .shape-2:hover:before {
  opacity: 1;
  visibility: visible;
}
.productivity-area .section-title .shape-2:before {
  content: "";
  width: 150px;
  aspect-ratio: 100/100;
  background-image: url(../imgs/shape/shape-4.webp);
  background-size: contain;
  position: absolute;
  top: 21px;
  left: -117px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.1s;
  display: inline-block;
}
@media only screen and (max-width: 1919px) {
  .productivity-area .section-title .shape-2:before {
    width: 100px;
  }
}
@media only screen and (max-width: 1399px) {
  .productivity-area .section-title .shape-2:before {
    width: 90px;
  }
}
@media only screen and (max-width: 1199px) {
  .productivity-area .section-title .shape-2:before {
    width: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .productivity-area .section-title .shape-2:before {
    left: -67px;
  }
}
.productivity-area .section-title .shape-3:hover:before {
  opacity: 1;
  visibility: visible;
}
.productivity-area .section-title .shape-3:before {
  content: "";
  width: 150px;
  aspect-ratio: 100/100;
  background-image: url(../imgs/shape/shape-6.webp);
  background-size: contain;
  position: absolute;
  bottom: -177px;
  left: 523px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.1s;
  display: inline-block;
}
@media only screen and (max-width: 1919px) {
  .productivity-area .section-title .shape-3:before {
    width: 100px;
    bottom: -137px;
  }
}
@media only screen and (max-width: 1399px) {
  .productivity-area .section-title .shape-3:before {
    width: 90px;
    bottom: -107px;
  }
}
@media only screen and (max-width: 1199px) {
  .productivity-area .section-title .shape-3:before {
    width: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .productivity-area .section-title .shape-3:before {
    left: 473px;
    bottom: -57px;
  }
}

/* text slider area style  */
.text-slider-active .swiper-slide {
  width: auto;
}

.text-slider {
  padding-top: 35px;
  padding-bottom: 35px;
  border-bottom: 1px solid var(--border);
}
@media only screen and (max-width: 1199px) {
  .text-slider {
    padding-top: 25px;
    padding-bottom: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .text-slider {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
.text-slider .swiper-wrapper {
  transition-timing-function: linear !important;
}

.text-slider-box {
  padding-bottom: 3px;
  border-bottom: 1px solid var(--border);
}

.text-slider-item .title {
  font-size: 36px;
  font-weight: 400;
  line-height: 1;
  text-transform: uppercase;
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 1919px) {
  .text-slider-item .title {
    font-size: 26px;
  }
}
@media only screen and (max-width: 1199px) {
  .text-slider-item .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .text-slider-item .title {
    font-size: 20px;
  }
}
.text-slider-item .title .dot {
  width: 10px;
  height: 10px;
  background-color: var(--primary);
  border-radius: 10px;
  margin-inline-end: 35px;
  display: inline-block;
}

/* design agency page css */
.body-design-agency.dark .section-title span {
  color: rgba(255, 255, 255, 0.4);
}
@media (min-width: 1650px) {
  .body-design-agency .container.large {
    max-width: 1650px;
  }
}
.body-design-agency .section-subtitle {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--primary);
}

.hero-area-4 {
  background: #C4F012;
}
.hero-area-4 .section-content {
  padding-top: 120px;
}
@media only screen and (max-width: 1199px) {
  .hero-area-4 .section-content {
    padding-top: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content {
    padding-top: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content {
    padding-top: 30px;
  }
}
.hero-area-4 .section-content__top {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  padding-bottom: 110px;
}
@media only screen and (max-width: 1199px) {
  .hero-area-4 .section-content__top {
    padding-bottom: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content__top {
    padding-bottom: 45px;
  }
}
.hero-area-4 .section-content__top-left {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}
.hero-area-4 .section-content__list {
  position: relative;
  padding-top: 20px;
}
.hero-area-4 .section-content__list::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 200px;
  height: 1px;
  background-color: var(--black);
}
.hero-area-4 .section-content__list li {
  color: var(--black);
  font-size: 18px;
  line-height: 22px;
}
.hero-area-4 .section-content__right {
  margin-right: 187px;
}
@media only screen and (max-width: 1199px), only screen and (max-width: 1399px) {
  .hero-area-4 .section-content__right {
    margin-right: 0;
  }
}
.hero-area-4 .section-content__right .section-title {
  font-size: 80px;
  font-style: normal;
  font-weight: 400;
  line-height: 70px;
  font-family: var(--font_thunder);
  text-transform: uppercase;
  letter-spacing: 0px;
  color: var(--black);
}
@media only screen and (max-width: 1199px) {
  .hero-area-4 .section-content__right .section-title {
    font-size: 65px;
    line-height: 60px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content__right .section-title {
    font-size: 50px;
    line-height: 50px;
  }
}
.hero-area-4 .section-content__right .section-title span {
  color: rgba(17, 17, 17, 0.5);
  font-style: italic;
  font-weight: 400;
}
.hero-area-4 .section-content__video img {
  max-width: 240px;
  height: 126px;
}
.hero-area-4 .section-content .title-wrapper {
  position: relative;
}
.hero-area-4 .section-content .title-wrapper .section-title {
  font-size: 350px;
  font-weight: 500;
  line-height: 0.8;
  letter-spacing: -8px;
  text-transform: uppercase;
  font-family: var(--font_thunder);
  color: var(--black);
}
@media only screen and (max-width: 1919px) {
  .hero-area-4 .section-content .title-wrapper .section-title {
    font-size: 250px;
    letter-spacing: 0px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-4 .section-content .title-wrapper .section-title {
    font-size: 220px;
    letter-spacing: 0px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-4 .section-content .title-wrapper .section-title {
    font-size: 180px;
    letter-spacing: 0px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content .title-wrapper .section-title {
    font-size: 140px;
    letter-spacing: 0px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content .title-wrapper .section-title {
    font-size: 100px;
    letter-spacing: 0px;
  }
}
@media (max-width: 575px) {
  .hero-area-4 .section-content .title-wrapper .section-title {
    font-size: 80px;
    letter-spacing: 0px;
  }
}
.hero-area-4 .section-content .title-wrapper .section-title .bg {
  width: 200px;
  height: 30px;
  margin: 0px -25px 0px -100px;
  display: inline-flex;
  background-color: var(--black);
}
@media only screen and (max-width: 1199px), only screen and (max-width: 1399px) {
  .hero-area-4 .section-content .title-wrapper .section-title .bg {
    width: 150px;
    height: 22px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content .title-wrapper .section-title .bg {
    width: 90px;
    height: 15px;
    margin: 0px -15px 0px -70px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content .title-wrapper .section-title .bg {
    display: none;
  }
}
.hero-area-4 .section-content .title-wrapper .section-title sup {
  display: inline-flex;
  font-size: 30px;
  border: 4px solid;
  width: 37px;
  border-radius: 20px;
  height: 44px;
  align-items: center;
  justify-content: center;
  text-align: center;
  top: -215px;
  left: -35px;
  padding-right: 6px;
  padding-top: 4px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-4 .section-content .title-wrapper .section-title sup {
    top: -140px;
    padding-right: 0;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-4 .section-content .title-wrapper .section-title sup {
    top: -122px;
    padding-right: 0;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-4 .section-content .title-wrapper .section-title sup {
    top: -98px;
    left: -30px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content .title-wrapper .section-title sup {
    top: -72px;
    left: -20px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content .title-wrapper .section-title sup {
    display: none;
  }
}
.hero-area-4 .section-content .title-wrapper .decisions {
  max-width: 330px;
  font-size: 20px;
  line-height: 30px;
  color: var(--black);
  position: absolute;
  bottom: 50px;
  right: 543px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-4 .section-content .title-wrapper .decisions {
    right: 400px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-4 .section-content .title-wrapper .decisions {
    right: 270px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-4 .section-content .title-wrapper .decisions {
    right: 190px;
    bottom: 25px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content .title-wrapper .decisions {
    position: inherit;
    right: 0;
    margin-top: 30px;
    max-width: 100%;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content .title-wrapper .decisions {
    margin-top: 50px;
  }
}
.hero-area-4 .section-content__bottom {
  display: flex;
  justify-content: space-between;
  padding-top: 50px;
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content__bottom {
    flex-wrap: wrap;
    padding-top: 0px;
  }
}
.hero-area-4 .section-content__bottom .social-links {
  max-width: 266px;
}
@media only screen and (max-width: 767px) {
  .hero-area-4 .section-content__bottom .social-links {
    max-width: 100%;
    margin-bottom: 30px;
  }
}
.hero-area-4 .section-content__bottom .social-links li {
  display: inline-block;
  margin-right: 15px;
  margin-bottom: 16px;
}
.hero-area-4 .section-content__bottom .social-links li a {
  font-size: 18px;
  color: var(--black);
  position: relative;
}
.hero-area-4 .section-content__bottom .social-links li a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: currentColor;
  transition: all 0.3s;
}
.hero-area-4 .section-content__bottom .social-links li a:hover::before {
  width: 0;
}
.hero-area-4 .section-content__thumb {
  margin-right: -144px;
  text-align: right;
}
@media only screen and (max-width: 991px) {
  .hero-area-4 .section-content__thumb {
    margin-right: -40px;
  }
}
@media (max-width: 575px) {
  .hero-area-4 .section-content__thumb {
    margin-right: 0;
  }
}

/* award area 2 style  */
.featured-work-area-2-inner .section-header {
  border-top: 1px solid var(--border);
  padding-top: 50px;
}
@media only screen and (max-width: 1199px) {
  .featured-work-area-2-inner .section-header {
    padding-top: 30px;
  }
}
.featured-work-area-2-inner .section-title {
  letter-spacing: 0;
  font-size: 80px;
  font-weight: 400;
}
@media only screen and (max-width: 1399px) {
  .featured-work-area-2-inner .section-title {
    font-size: 70px;
  }
}
@media only screen and (max-width: 1199px) {
  .featured-work-area-2-inner .section-title {
    font-size: 48px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-work-area-2-inner .section-title {
    font-size: 40px;
  }
  .featured-work-area-2-inner .section-title br {
    display: none;
  }
}
.featured-work-area-2-inner .title-wrapper {
  margin-top: 5px;
}
.featured-work-area-2-inner .section-title-wrapper {
  display: flex;
  gap: 30px;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .featured-work-area-2-inner .section-title-wrapper {
    flex-direction: column;
    margin-bottom: 40px;
    gap: 10px;
  }
}
.featured-work-area-2-inner .description {
  max-width: 442px;
  transform: translate(-130px, 100%);
  margin-top: 76px;
}
@media only screen and (max-width: 1399px) {
  .featured-work-area-2-inner .description {
    transform: translate(-50px, 100%);
  }
}
@media only screen and (max-width: 1199px) {
  .featured-work-area-2-inner .description {
    transform: translate(10px, 100%);
  }
}
@media only screen and (max-width: 991px) {
  .featured-work-area-2-inner .description {
    transform: translate(-88px, 100%);
    max-width: 270px;
    margin-top: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-work-area-2-inner .description {
    transform: translate(0px, 0%);
    max-width: 100%;
    margin-top: 0px;
  }
}
.featured-work-area-2-inner .description p {
  font-size: 30px;
  font-weight: 400;
  line-height: 38px;
}
@media only screen and (max-width: 1199px) {
  .featured-work-area-2-inner .description p {
    font-size: 25px;
    line-height: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-work-area-2-inner .description p {
    font-size: 20px;
    line-height: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-work-area-2-inner .description p {
    font-size: 18px;
    line-height: 25px;
  }
}

.featured-work-wrapper-2 {
  display: grid;
  gap: 295px;
  grid-template-columns: auto auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1919px) {
  .featured-work-wrapper-2 {
    gap: 200px;
  }
}
@media only screen and (max-width: 1399px) {
  .featured-work-wrapper-2 {
    gap: 150px;
  }
}
@media only screen and (max-width: 1199px) {
  .featured-work-wrapper-2 {
    gap: 100px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-work-wrapper-2 {
    gap: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-work-wrapper-2 {
    gap: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-work-wrapper-2 {
    grid-template-columns: auto;
  }
}
.featured-work-wrapper-2 > *:nth-child(3n+3) {
  grid-column: span 2;
  margin-left: auto;
  margin-right: auto;
}
@media only screen and (max-width: 767px) {
  .featured-work-wrapper-2 > *:nth-child(3n+3) {
    grid-column: auto;
  }
}
.featured-work-wrapper-2 > *:nth-child(3n+2) {
  margin-top: auto;
  margin-bottom: 8px;
}
.featured-work-wrapper-2 > *:nth-child(2) {
  margin-top: 357px;
}
@media only screen and (max-width: 1399px) {
  .featured-work-wrapper-2 > *:nth-child(2) {
    margin-top: 290px;
  }
}
@media only screen and (max-width: 1199px) {
  .featured-work-wrapper-2 > *:nth-child(2) {
    margin-top: 180px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-work-wrapper-2 > *:nth-child(2) {
    margin-top: 130px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-work-wrapper-2 > *:nth-child(2) {
    margin-top: 0;
  }
}
.featured-work-wrapper-2 > *:nth-child(3) {
  max-width: 750px;
}
@media only screen and (max-width: 1399px), only screen and (max-width: 1919px) {
  .featured-work-wrapper-2 > *:nth-child(3) {
    max-width: 565px;
  }
}
@media only screen and (max-width: 1199px) {
  .featured-work-wrapper-2 > *:nth-child(3) {
    max-width: 435px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-work-wrapper-2 > *:nth-child(3) {
    max-width: 358px;
  }
}
.featured-work-wrapper-2 .featured-work-box {
  width: 100%;
}
.featured-work-wrapper-2 .featured-work-box .content {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
@media only screen and (max-width: 991px) {
  .featured-work-wrapper-2 .featured-work-box .content {
    flex-wrap: wrap;
    gap: 10px;
  }
}
.featured-work-wrapper-2 .featured-work-box .content .title {
  font-size: 30px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.6px;
  text-transform: uppercase;
}
.featured-work-wrapper-2 .featured-work-box .content .meta .tag {
  text-transform: uppercase;
  font-size: 14px;
}
.featured-work-wrapper-2 .featured-work-box .thumb {
  overflow: hidden;
  display: inline-block;
  width: 100%;
  position: relative;
}
.featured-work-wrapper-2 .featured-work-box .thumb::before, .featured-work-wrapper-2 .featured-work-box .thumb::after {
  position: absolute;
  width: 0;
  height: 101%;
  content: "";
  top: -1px;
  transition: all 0.5s;
  background-color: var(--white);
}
.dark .featured-work-wrapper-2 .featured-work-box .thumb::before, .dark .featured-work-wrapper-2 .featured-work-box .thumb::after {
  background-color: var(--black);
}
.featured-work-wrapper-2 .featured-work-box .thumb::before {
  left: -1px;
}
.featured-work-wrapper-2 .featured-work-box .thumb::after {
  right: -1px;
}
.featured-work-wrapper-2 .featured-work-box .thumb span {
  display: block;
}
.featured-work-wrapper-2 .featured-work-box .thumb span::before, .featured-work-wrapper-2 .featured-work-box .thumb span::after {
  position: absolute;
  width: 101%;
  height: 0;
  content: "";
  left: -1px;
  transition: all 0.5s;
  background-color: var(--white);
}
.dark .featured-work-wrapper-2 .featured-work-box .thumb span::before, .dark .featured-work-wrapper-2 .featured-work-box .thumb span::after {
  background-color: var(--black);
}
.featured-work-wrapper-2 .featured-work-box .thumb span::before {
  top: -1px;
}
.featured-work-wrapper-2 .featured-work-box .thumb span::after {
  bottom: -1px;
}
.featured-work-wrapper-2 .featured-work-box .thumb img {
  width: 100%;
  height: 100%;
  cursor: none;
  object-fit: cover;
}
.featured-work-wrapper-2 .featured-work-box .thumb:hover::before, .featured-work-wrapper-2 .featured-work-box .thumb:hover::after {
  width: 30px;
}
.featured-work-wrapper-2 .featured-work-box .thumb:hover span::before, .featured-work-wrapper-2 .featured-work-box .thumb:hover span::after {
  height: 30px;
}
.featured-work-wrapper-2 .content-wapper {
  margin-right: auto;
  text-align: left;
}
.featured-work-wrapper-2 .view-button .desc {
  margin-bottom: 90px;
  max-width: 365px;
  font-size: 20px;
}
@media only screen and (max-width: 1199px) {
  .featured-work-wrapper-2 .view-button .desc {
    max-width: 100%;
    margin-bottom: 50px;
  }
}

/* capabilities area style  */
.capabilities-area-inner {
  position: relative;
  padding-bottom: 136px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-inner {
    padding-bottom: 106px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area-inner {
    padding-bottom: 86px;
  }
}
.capabilities-area-inner .pin-spacer {
  pointer-events: none;
}
.capabilities-area .section-content-wrapper {
  margin-top: 50px;
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 1fr 1235px;
  border-top: 1px solid var(--border);
  padding-top: 45px;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area .section-content-wrapper {
    grid-template-columns: 1fr 950px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area .section-content-wrapper {
    grid-template-columns: 1fr 800px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area .section-content-wrapper {
    grid-template-columns: 1fr 600px;
  }
}
@media only screen and (max-width: 991px) {
  .capabilities-area .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.capabilities-area .section-content .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  max-width: 245px;
}
@media only screen and (max-width: 991px) {
  .capabilities-area .section-content .text {
    max-width: 545px;
  }
}
.capabilities-area .section-content .text-wrapper {
  margin-top: 63px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area .section-content .text-wrapper {
    margin-top: 43px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area .section-content .text-wrapper {
    margin-top: 23px;
  }
}
.capabilities-area .capability-wrapper-box {
  margin-top: 5px;
}
@media only screen and (max-width: 767px) {
  .capabilities-area .capability-wrapper {
    border-top: 1px solid var(--border);
  }
}
@media only screen and (max-width: 767px) {
  .capabilities-area .capability-box {
    border-bottom: 1px solid var(--border);
    padding-bottom: 20px;
    padding-top: 20px;
  }
}
.capabilities-area .capability-box-inner {
  display: grid;
  gap: 10px 60px;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
}
@media (max-width: 575px) {
  .capabilities-area .capability-box-inner {
    grid-template-columns: 1fr;
  }
}
.capabilities-area .capability-box:hover .thumb img, .capabilities-area .capability-box.active .thumb img {
  opacity: 1;
  transform: scale(1);
}
.capabilities-area .capability-box .title {
  font-size: 100px;
  font-weight: 400;
  line-height: 0.85;
  text-transform: uppercase;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area .capability-box .title {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area .capability-box .title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area .capability-box .title {
    font-size: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .capabilities-area .capability-box .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .capabilities-area .capability-box .title {
    font-size: 35px;
  }
}
.capabilities-area .capability-box .title.rr-btn-underline {
  padding-bottom: 0;
  color: rgba(17, 17, 17, 0.4);
}
.dark .capabilities-area .capability-box .title.rr-btn-underline {
  color: rgba(255, 255, 255, 0.4);
}
.capabilities-area .capability-box .title.rr-btn-underline::before {
  height: 5px;
  transition: 0.5s;
}
@media only screen and (max-width: 1399px) {
  .capabilities-area .capability-box .title.rr-btn-underline::before {
    height: 3px;
  }
}
@media only screen and (max-width: 991px) {
  .capabilities-area .capability-box .title.rr-btn-underline::before {
    height: 2px;
  }
}
.capabilities-area .capability-box .thumb {
  display: flex;
  gap: 15px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area .capability-box .thumb {
    gap: 10px;
  }
}
.capabilities-area .capability-box .thumb img {
  width: 70px;
  height: 70px;
  border-radius: 15px;
  object-fit: cover;
  opacity: 0;
  transform: scale(0);
  transform-origin: top right;
  transition: all 0.5s;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area .capability-box .thumb img {
    width: 64px;
    height: 64px;
    border-radius: 10px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area .capability-box .thumb img {
    width: 50px;
    height: 50px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area .capability-box .thumb img {
    width: 40px;
    height: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .capabilities-area .capability-box .thumb img {
    opacity: 1;
    transform: scale(1);
  }
}

/* client area 4 style  */
.client-area-4 {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  background-color: var(--white);
  z-index: 1;
  pointer-events: auto;
}
.dark .client-area-4 {
  background-color: var(--black);
}
.client-area-4-inner {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  overflow: hidden;
}
.client-area-4 .video-wrapper-box {
  display: flex;
  align-items: center;
}
.client-area-4 .video-wrapper-box .thumb {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .client-area-4 .video-wrapper-box .thumb {
    width: 80px;
    height: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .client-area-4 .video-wrapper-box .thumb {
    width: 70px;
    height: 70px;
  }
}
.client-area-4 .video-wrapper-box .btn-circle {
  width: 90px;
  height: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-color: #C4F012;
  color: var(--black);
  border-radius: 50%;
  font-size: 26px;
  outline: 5px solid var(--white);
  margin-left: -15px;
}
.dark .client-area-4 .video-wrapper-box .btn-circle {
  outline-color: var(--black);
}
@media only screen and (max-width: 1919px) {
  .client-area-4 .video-wrapper-box .btn-circle {
    width: 80px;
    height: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .client-area-4 .video-wrapper-box .btn-circle {
    width: 70px;
    height: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .client-area-4 .video-wrapper-box .btn-circle {
    margin-left: -35px;
  }
}
.client-area-4 .clients-wrapper-box {
  border-left: 1px solid var(--border);
  padding-left: 30px;
  margin-left: 30px;
  padding-top: 50px;
  padding-bottom: 50px;
}
@media only screen and (max-width: 1919px) {
  .client-area-4 .clients-wrapper-box {
    padding-top: 35px;
    padding-bottom: 35px;
  }
}
@media only screen and (max-width: 1199px) {
  .client-area-4 .clients-wrapper-box {
    padding-top: 25px;
    padding-bottom: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .client-area-4 .clients-wrapper-box {
    padding-left: 20px;
    margin-left: 20px;
  }
}
.client-area-4 .clients-wrapper {
  align-items: center;
  animation: marquee-081a87f6 20s linear infinite;
  display: flex;
  flex-wrap: nowrap;
  width: max-content;
}
@keyframes marquee-081a87f6 {
  to {
    transform: translate(-50%);
  }
}
.client-area-4 .client-box {
  margin-right: 80px;
}
@media only screen and (max-width: 1199px) {
  .client-area-4 .client-box {
    margin-right: 50px;
  }
}
.client-area-4 .client-box img {
  opacity: 0.3;
}
.dark .client-area-4 .client-box img {
  opacity: 1;
}

/* about area 3 style  */
.about-area-3 .section-content .text {
  font-size: 30px;
  font-weight: 400;
  line-height: 1.26;
  max-width: 660px;
}
@media only screen and (max-width: 1919px) {
  .about-area-3 .section-content .text {
    font-size: 24px;
    max-width: 550px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-3 .section-content .text {
    font-size: 22px;
    max-width: 500px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-3 .section-content .text {
    font-size: 20px;
    max-width: 450px;
    line-height: 1.4;
  }
}
.about-area-3 .section-content .text-wrapper {
  margin-top: -124px;
  margin-left: 15%;
}
@media only screen and (max-width: 1399px) {
  .about-area-3 .section-content .text-wrapper {
    margin-top: -104px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-3 .section-content .text-wrapper {
    margin-top: -84px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-3 .section-content .text-wrapper {
    margin-top: -64px;
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .about-area-3 .section-content .text-wrapper {
    margin-top: 40px;
  }
}
.about-area-3 .section-content .btn-wrapper {
  margin-top: 61px;
  margin-left: 15%;
}
@media only screen and (max-width: 1919px) {
  .about-area-3 .section-content .btn-wrapper {
    margin-top: 41px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-3 .section-content .btn-wrapper {
    margin-left: 0;
  }
}
.about-area-3 .about-thumb {
  width: 100%;
  aspect-ratio: 100/74;
  position: relative;
  height: 100%;
}
.about-area-3 .about-thumb .thumb-1 {
  position: absolute;
  top: 11%;
  left: 0;
  width: 35%;
  -o-object-fit: cover;
  object-fit: cover;
  aspect-ratio: 100/67;
  z-index: 1;
}
.about-area-3 .about-thumb .thumb-2 {
  position: absolute;
  top: 0;
  left: 30%;
  width: 39%;
  object-fit: cover;
  aspect-ratio: 100/142;
}
.about-area-3 .about-thumb .thumb-3 {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30%;
  object-fit: cover;
  aspect-ratio: 100/136;
}
.about-area-3 .about-thumb .thumb-4 {
  position: absolute;
  bottom: 18%;
  left: 15%;
  width: 14%;
  object-fit: cover;
  aspect-ratio: 100/130;
}
@media only screen and (max-width: 991px) {
  .about-area-3 .about-thumb .thumb-4 {
    bottom: 28%;
  }
}

/* award area 2 style  */
.award-area-2 {
  background-color: var(--bg);
}
.award-area-2 .section-header {
  margin-top: 50px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 45px;
}
.award-area-2 .section-subtitle {
  color: var(--white);
}
.award-area-2 .section-title {
  color: var(--white);
  max-width: 780px;
}
@media only screen and (max-width: 1919px) {
  .award-area-2 .section-title {
    max-width: 680px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-2 .section-title {
    max-width: 480px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-2 .section-title {
    max-width: 430px;
  }
}
@media only screen and (max-width: 991px) {
  .award-area-2 .section-title {
    max-width: 330px;
  }
}
.award-area-2 .section-title span {
  color: rgba(255, 255, 255, 0.4);
}
.award-area-2 .title-wrapper {
  margin-top: 5px;
}
.award-area-2 .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .award-area-2 .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-2 .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-2 .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .award-area-2 .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.award-area-2 .award-wrapper-box {
  max-width: 1235px;
  margin-left: auto;
  margin-top: 85px;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1919px) {
  .award-area-2 .award-wrapper-box {
    max-width: 1000px;
    margin-top: 55px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-2 .award-wrapper-box {
    max-width: 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-2 .award-wrapper-box {
    max-width: 750px;
    margin-top: 45px;
  }
}
.award-area-2 .award-wrapper {
  border-top: 1px solid rgb(41, 41, 41);
}
.award-area-2 .award-box {
  border-bottom: 1px solid rgb(41, 41, 41);
  padding-top: 40px;
  padding-bottom: 40px;
  display: grid;
  gap: 20px 50px;
  grid-template-columns: 280px 1fr 100px;
  align-items: center;
  transition: all 0.5s;
}
@media only screen and (max-width: 1919px) {
  .award-area-2 .award-box {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-2 .award-box {
    grid-template-columns: 180px 1fr 100px;
  }
}
@media only screen and (max-width: 767px) {
  .award-area-2 .award-box {
    grid-template-columns: 1fr 1fr;
  }
}
.award-area-2 .award-box:hover {
  background-color: #171717;
}
.dark .award-area-2 .award-box:hover {
  background-color: #292828;
}
@media only screen and (max-width: 767px) {
  .award-area-2 .award-box:hover {
    background-color: transparent;
  }
}
.award-area-2 .award-box:hover .category {
  transform: translateX(30px);
}
@media only screen and (max-width: 767px) {
  .award-area-2 .award-box:hover .category {
    transform: translateX(0px);
  }
}
.award-area-2 .award-box:hover .year {
  transform: translateX(-30px);
}
@media only screen and (max-width: 767px) {
  .award-area-2 .award-box:hover .year {
    transform: translateX(0px);
  }
}
.award-area-2 .award-box .category {
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  display: inline-block;
  color: var(--white);
  transition: all 0.5s;
}
.award-area-2 .award-box .award {
  font-size: 24px;
  font-weight: 400;
  line-height: 18px;
  color: var(--white);
}
@media only screen and (max-width: 1919px) {
  .award-area-2 .award-box .award {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .award-area-2 .award-box .award {
    order: 3;
    grid-column: span 2;
  }
}
.award-area-2 .award-box .year {
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  display: inline-block;
  color: var(--white);
  transition: all 0.5s;
  text-align: right;
}

/* service area 4 style  */
.service-area-4 {
  position: relative;
  width: 100vw;
  overflow: hidden;
}

.services-wrapper-4 {
  gap: 100px;
  width: fit-content;
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 {
    display: grid;
    gap: 50px;
  }
}
.services-wrapper-4 .service-box {
  border-top: 1px solid var(--primary);
  width: 760px;
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 .service-box {
    width: 100%;
  }
}
.services-wrapper-4 .service-box .number {
  font-family: var(--font_thunder);
  font-size: 350px;
  font-weight: 400;
  line-height: 0.7;
  letter-spacing: -0.02em;
  display: inline-block;
  color: var(--primary);
  margin-top: 80px;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-4 .service-box .number {
    font-size: 200px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-4 .service-box .number {
    font-size: 180px;
    margin-top: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-4 .service-box .number {
    font-size: 150px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 .service-box .number {
    font-size: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 .service-box .number {
    font-size: 100px;
  }
}
.services-wrapper-4 .service-box .title {
  font-family: var(--font_thunder);
  font-size: 100px;
  text-transform: uppercase;
  margin-top: 40px;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-4 .service-box .title {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-4 .service-box .title {
    font-size: 60px;
    margin-top: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-4 .service-box .title {
    font-size: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 .service-box .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .services-wrapper-4 .service-box .title {
    font-size: 35px;
  }
}
.services-wrapper-4 .service-box .feature-list {
  border-top: 1px dashed #878482;
  margin-top: 34px;
}
.dark .services-wrapper-4 .service-box .feature-list {
  border-color: #6F6D6C;
}
.services-wrapper-4 .service-box .feature-list li {
  font-family: var(--font_thunder);
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
  text-transform: uppercase;
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #878482;
  padding-top: 13px;
  padding-bottom: 8px;
}
.dark .services-wrapper-4 .service-box .feature-list li {
  border-color: #6F6D6C;
}
.services-wrapper-4 .service-box .feature-list li:before {
  content: "+";
  margin-right: 4px;
}
.services-wrapper-4 .service-thumb {
  width: 1920px;
  position: relative;
  overflow: hidden;
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 .service-thumb {
    width: 100%;
    position: inherit;
  }
}
.services-wrapper-4 .service-thumb img {
  width: 100%;
  height: auto;
  object-fit: cover;
}
.services-wrapper-4 .service-thumb-line-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  rotate: 180deg;
}
@media only screen and (max-width: 991px) {
  .services-wrapper-4 .service-thumb-line-wrapper {
    display: none;
  }
}
.services-wrapper-4 .service-thumb-line-wrapper span {
  width: 70px;
  height: 1000px;
  background-color: var(--white);
  transform-origin: right center;
  margin-left: -1px;
}
.dark .services-wrapper-4 .service-thumb-line-wrapper span {
  background-color: var(--black);
}

/* creative agency page css */
.body-creative-agency {
  position: relative;
  z-index: 100;
  background-color: #FCF7F3;
}
.body-creative-agency.dark {
  --primary: #FCF7F3;
}
.body-creative-agency.dark .rr-btn::before {
  background-color: var(--black);
}
.body-creative-agency.dark .rr-btn.btn-border-white {
  border-color: rgba(17, 17, 17, 0.1);
}
.body-creative-agency.dark .header-area-2 .side-toggle {
  background-color: #292828;
}
.body-creative-agency .body-bg {
  position: absolute;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: -1;
}
.body-creative-agency .body-bg img {
  width: 100%;
}
@media (min-width: 1850px) {
  .body-creative-agency .container.large {
    max-width: 1850px;
    --container-max-widths: 1820px;
  }
}
.body-creative-agency .section-subtitle {
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  display: inline-block;
  text-transform: uppercase;
  color: var(--primary);
  letter-spacing: 1px;
}
@media (min-width: 1920px) {
  .body-creative-agency .section-title {
    font-size: 110px;
  }
}
.body-creative-agency .rr-btn::before {
  background-color: #FCF7F3;
}

/* hero area 2 style  */
.hero-area-2-inner {
  padding-top: 100px;
}
.hero-area-2 .section-title {
  font-size: 200px;
  font-weight: 310;
  line-height: 0.85;
  letter-spacing: -0.09em;
  text-transform: uppercase;
  margin-left: 60px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .section-title {
    font-size: 140px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .section-title {
    font-size: 120px;
    margin-left: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-2 .section-title {
    font-size: 100px;
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-2 .section-title {
    font-size: 56px;
    line-height: 0.95;
  }
}
@media (max-width: 575px) {
  .hero-area-2 .section-title {
    font-size: 40px;
  }
}
.hero-area-2 .section-title .title-shape-1 {
  height: 143px;
  margin-left: 14px;
  margin-top: -35px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .section-title .title-shape-1 {
    height: 100px;
    margin-top: -25px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .section-title .title-shape-1 {
    height: 86px;
    margin-top: -23px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-2 .section-title .title-shape-1 {
    height: 70px;
    margin-top: -18px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-2 .section-title .title-shape-1 {
    display: none;
  }
}
.hero-area-2 .section-title .title-video {
  height: 150px;
  margin-right: 10px;
  margin-left: -60px;
  display: inline-block;
  margin-top: -40px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .section-title .title-video {
    height: 100px;
    margin-top: -25px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .section-title .title-video {
    height: 85px;
    margin-left: -30px;
    margin-top: -22px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-2 .section-title .title-video {
    height: 70px;
    margin-top: -18px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-2 .section-title .title-video {
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-2 .section-title .title-video {
    display: none;
  }
}
.hero-area-2 .section-content {
  margin-top: -450px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .section-content {
    margin-top: -330px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .section-content {
    margin-top: -240px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-2 .section-content {
    margin-top: 30px;
  }
}
.hero-area-2 .section-content .text-wrapper {
  max-width: 565px;
  margin-left: auto;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .section-content .text-wrapper {
    max-width: 455px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .section-content .text-wrapper {
    max-width: 345px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-2 .section-content .text-wrapper {
    max-width: 100%;
  }
}
.hero-area-2 .section-content .info-text {
  font-family: var(--font_sequelsansromanbody);
  font-size: 18px;
  font-weight: 310;
  line-height: 20px;
  letter-spacing: -0.05em;
  max-width: 211px;
  color: var(--primary);
}
.hero-area-2 .section-content .info-text span {
  text-decoration: underline;
}
.hero-area-2 .section-content .about-text {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  margin-top: 474px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .section-content .about-text {
    font-size: 24px;
    margin-top: 304px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .section-content .about-text {
    margin-top: 204px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-2 .section-content .about-text {
    font-size: 22px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-2 .section-content .about-text {
    margin-top: 34px;
  }
}
.hero-area-2 .hero-thumb {
  max-width: 1290px;
  margin-top: 44px;
  margin-left: auto;
}
@media only screen and (max-width: 1919px) {
  .hero-area-2 .hero-thumb {
    max-width: 990px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-2 .hero-thumb {
    max-width: 890px;
  }
}

/* about area 2 style  */
.about-area-2 {
  overflow-x: clip;
}
.about-area-2 .section-title {
  max-width: 1130px;
}
@media only screen and (max-width: 1919px) {
  .about-area-2 .section-title {
    max-width: 830px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-title {
    max-width: 630px;
  }
}
.about-area-2 .section-header {
  margin-top: 69px;
}
.about-area-2 .section-content {
  min-height: 300vh;
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content {
    min-height: auto;
  }
}
.about-area-2 .section-content .year-wrapper {
  position: relative;
  height: 355px;
}
@media only screen and (max-width: 1919px) {
  .about-area-2 .section-content .year-wrapper {
    height: 242px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content .year-wrapper {
    height: 213px;
    height: auto;
  }
}
.about-area-2 .section-content .year-since {
  font-size: 500px;
  font-weight: 315;
  line-height: 0.71;
  letter-spacing: -0.1em;
  font-family: var(--font_sequelsansmediumbody);
  white-space: nowrap;
  text-transform: uppercase;
  position: absolute;
  top: 0;
  opacity: 1;
}
@media only screen and (max-width: 1919px) {
  .about-area-2 .section-content .year-since {
    font-size: 340px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content .year-since {
    font-size: 235px;
    text-align: left;
    position: relative;
    white-space: wrap;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-2 .section-content .year-since {
    font-size: 180px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-2 .section-content .year-since {
    font-size: 110px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area-2 .section-content .year-since {
    font-size: 100px;
  }
}
@media (max-width: 575px) {
  .about-area-2 .section-content .year-since {
    font-size: 60px;
  }
}
.about-area-2 .section-content .year-since .first-text {
  width: var(--container-max-widths);
  display: inline-block;
  text-align: right;
  padding-right: 40px;
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content .year-since .first-text {
    text-align: left;
    padding-right: 0;
    width: auto;
  }
}
.about-area-2 .section-content .year-since .last-text {
  width: var(--container-max-widths);
  display: inline-block;
  text-align: center;
  position: relative;
  transform-origin: top center;
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content .year-since .last-text {
    display: none;
  }
}
.about-area-2 .section-content .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  max-width: 515px;
}
.about-area-2 .section-content .text-wrapper {
  margin-top: 91px;
  max-width: 770px;
  margin-left: auto;
}
@media only screen and (max-width: 1919px) {
  .about-area-2 .section-content .text-wrapper {
    max-width: 570px;
    margin-top: 61px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content .text-wrapper {
    max-width: 670px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-2 .section-content .text-wrapper {
    max-width: 550px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-2 .section-content .text-wrapper {
    max-width: 100%;
    margin-top: 41px;
  }
}
.about-area-2 .section-content .btn-wrapper {
  margin-top: 38px;
  max-width: 770px;
  margin-left: auto;
}
@media only screen and (max-width: 1919px) {
  .about-area-2 .section-content .btn-wrapper {
    max-width: 570px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-2 .section-content .btn-wrapper {
    max-width: 670px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-2 .section-content .btn-wrapper {
    max-width: 550px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-2 .section-content .btn-wrapper {
    max-width: 100%;
  }
}

/* work area 2 style  */
.work-area-2 {
  position: relative;
}
@media (min-width: 992px) {
  .work-area-2-inner {
    padding-top: 100px !important;
  }
}
.work-area-2 .works-wrapper-head {
  display: grid;
  gap: 10px 60px;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 48px;
  align-items: flex-end;
}
@media only screen and (max-width: 1399px) {
  .work-area-2 .works-wrapper-head {
    margin-bottom: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .work-area-2 .works-wrapper-head {
    grid-template-columns: auto;
  }
}
.work-area-2 .works-wrapper-head .text {
  font-family: var(--font_sequelsansmediumbody);
  font-size: 30px;
  font-weight: 315;
  line-height: 27px;
  letter-spacing: -0.1em;
  text-transform: uppercase;
  color: var(--primary);
}
@media only screen and (max-width: 1399px) {
  .work-area-2 .works-wrapper-head .text {
    font-size: 22px;
  }
}
@media only screen and (max-width: 991px) {
  .work-area-2 .works-wrapper-head .text {
    font-size: 20px;
  }
}
.work-area-2 .works-wrapper-head > *:nth-child(2) {
  text-align: end;
}
@media only screen and (max-width: 767px) {
  .work-area-2 .works-wrapper-head > *:nth-child(2) {
    text-align: start;
  }
}

.works-wrapper-2 {
  display: grid;
  gap: 98px 20px;
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-2 {
    gap: 78px 20px;
  }
}
@media only screen and (max-width: 1399px) {
  .works-wrapper-2 {
    gap: 58px 20px;
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-2 {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .works-wrapper-2 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.works-wrapper-2 > *:nth-child(3) {
  grid-column-start: 4;
}
.works-wrapper-2 > *:nth-child(4) {
  grid-column-start: 2;
}
.works-wrapper-2 > *:nth-child(6) {
  grid-column-start: 1;
}
.works-wrapper-2 > *:nth-child(7) {
  grid-column-start: 3;
}
.works-wrapper-2 > *:nth-child(9) {
  grid-column-start: 2;
}
.works-wrapper-2 > *:nth-child(12) {
  grid-column-start: 3;
}
@media only screen and (max-width: 991px) {
  .works-wrapper-2 > *:nth-child(n) {
    grid-column-start: auto;
  }
}
.works-wrapper-2 .work-box {
  position: relative;
}
.works-wrapper-2 .work-box .thumb {
  overflow: hidden;
  position: relative;
}
.works-wrapper-2 .work-box .thumb:hover .t-btn {
  opacity: 1;
}
.works-wrapper-2 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-2 .work-box .thumb .t-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  padding: 10px 20px;
  display: inline-block;
  background-color: white;
  color: var(--black);
  border-radius: 50px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  margin: -25px 0 0 -65px;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  pointer-events: none;
}
.works-wrapper-2 .work-box .content {
  margin-top: 8px;
}
.works-wrapper-2 .work-box .title {
  font-size: 20px;
  font-weight: 500;
  line-height: 1.35;
  letter-spacing: -0.02em;
  font-family: var(--font_sequelsansmediumbody);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-2 .work-box .title {
    font-size: 18px;
  }
}
.works-wrapper-2 .work-box .meta {
  display: flex;
  gap: 5px;
  align-items: center;
}
.works-wrapper-2 .work-box .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  color: #999999;
  display: flex;
  align-items: center;
}
.works-wrapper-2 .work-box .meta span:not(:first-child):before {
  content: "";
  width: 6px;
  height: 1px;
  background-color: currentColor;
  display: inline-block;
  margin-inline-end: 5px;
}

.actually-area {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.actually-area-inner {
  position: relative;
}
.actually-area .section-title {
  max-width: 716px;
  text-align: center;
  margin-inline: auto;
}
.actually-area .t_line > div {
  background-image: linear-gradient(to right, var(--primary) 50%, #CDC9C6 50%);
  background-size: 200% 100%;
  background-position-x: 100%;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}
.dark .actually-area .t_line > div {
  background-image: linear-gradient(to right, var(--primary) 50%, #464646 51%);
}
.actually-area .bg-area {
  position: absolute;
  top: 40%;
  left: 51%;
  transform: translate(-50%, -50%) scale(0);
  background-color: var(--primary);
  width: 250px;
  height: 250px;
  border-radius: 50%;
}

/* service area style  */
.service-area-2-inner {
  margin-bottom: -2px;
}
.service-area-2 .section-header {
  position: relative;
  z-index: -1;
  transition: all 0.3s;
  margin-top: 91px;
  margin-bottom: 357px;
}
@media only screen and (max-width: 1919px) {
  .service-area-2 .section-header {
    margin-top: 61px;
    margin-bottom: 237px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-2 .section-header {
    margin-top: 41px;
    margin-bottom: 157px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-area-2 .section-header {
    margin-top: 31px;
    margin-bottom: 117px;
  }
}
@media only screen and (max-width: 991px) {
  .service-area-2 .section-header {
    margin-top: 21px;
    margin-bottom: 77px;
  }
}
.service-area-2 .services-wrapper-box {
  background-color: var(--primary);
  position: relative;
}

.service-content-wrapper {
  background-color: var(--primary);
}
.service-content-wrapper .service-content {
  display: grid;
  gap: 30px 60px;
  grid-template-columns: 705px auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1199px) {
  .service-content-wrapper .service-content {
    grid-template-columns: 405px auto;
  }
}
@media only screen and (max-width: 991px) {
  .service-content-wrapper .service-content {
    grid-template-columns: auto;
  }
}
.service-content-wrapper .service-content .text {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: rgba(252, 247, 243, 0.3);
}
@media only screen and (max-width: 1919px) {
  .service-content-wrapper .service-content .text {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-content-wrapper .service-content .text {
    font-size: 22px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-content-wrapper .service-content .text {
    font-size: 20px;
  }
}
.service-content-wrapper .service-content .text.text-invert > div {
  background-image: linear-gradient(to right, rgb(252, 247, 243) 50%, rgba(252, 247, 243, 0.3) 51%);
}
.dark .service-content-wrapper .service-content .text.text-invert > div {
  background-image: linear-gradient(to right, rgb(17, 17, 17) 50%, rgba(17, 17, 17, 0.3) 51%);
}
.service-content-wrapper .service-content .btn-wrapper {
  margin-top: 54px;
}
.service-content-wrapper .service-content .text-wrapper {
  margin-top: 192px;
  max-width: 525px;
}
@media only screen and (max-width: 1919px) {
  .service-content-wrapper .service-content .text-wrapper {
    margin-top: 102px;
  }
}
@media only screen and (max-width: 991px) {
  .service-content-wrapper .service-content .text-wrapper {
    margin-top: 0;
  }
}
.service-content-wrapper .service-content .text-wrapper .text:not(:first-child) {
  margin-top: 35px;
}
.service-content-wrapper .service-content .section-info-wrapper .thumb {
  max-width: 250px;
  margin-left: auto;
}
@media only screen and (max-width: 991px) {
  .service-content-wrapper .service-content .section-info-wrapper .thumb {
    display: none;
  }
}

.services-wrapper-2 .service-box {
  display: grid;
  gap: 20px 50px;
  grid-template-columns: 215px 1fr 595px;
  border-top: 1px solid #292828;
  padding-top: 59px;
  padding-bottom: 72px;
  background-color: var(--primary);
  transition: all 0.5s;
}
.dark .services-wrapper-2 .service-box {
  border-color: #EAE3DD;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-2 .service-box {
    grid-template-columns: 215px 1fr 395px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-2 .service-box {
    grid-template-columns: 165px 1fr 395px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-2 .service-box {
    grid-template-columns: 115px 1fr 375px;
    padding-top: 49px;
    padding-bottom: 52px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-2 .service-box {
    grid-template-columns: 55px 1fr 285px;
    padding-top: 39px;
    padding-bottom: 42px;
  }
}
@media only screen and (max-width: 767px) {
  .services-wrapper-2 .service-box {
    grid-template-columns: auto;
  }
}
.services-wrapper-2 .service-box:last-child {
  border-bottom: 1px solid #292828;
}
.dark .services-wrapper-2 .service-box:last-child {
  border-color: #EAE3DD;
}
.services-wrapper-2 .service-box-wrapper {
  background-color: #292828;
}
.dark .services-wrapper-2 .service-box-wrapper {
  background-color: #EAE3DD;
}
.services-wrapper-2 .service-box-wrapper:hover .service-box {
  border-radius: 120px;
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-2 .service-box-wrapper:hover .service-box {
    border-radius: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-2 .service-box-wrapper:hover .service-box {
    border-radius: 0px;
  }
}
.services-wrapper-2 .service-box-wrapper:hover .service-box .number {
  transform: translateX(60px);
}
@media only screen and (max-width: 991px) {
  .services-wrapper-2 .service-box-wrapper:hover .service-box .number {
    transform: none;
  }
}
.services-wrapper-2 .service-box .number {
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  color: #FCF7F3;
  margin-top: 12px;
  transition: all 0.5s;
}
.dark .services-wrapper-2 .service-box .number {
  color: var(--black);
}
.services-wrapper-2 .service-box .title {
  font-size: 110px;
  font-weight: 310;
  line-height: 0.9;
  letter-spacing: -0.07em;
  color: #FCF7F3;
}
.dark .services-wrapper-2 .service-box .title {
  color: var(--black);
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-2 .service-box .title {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-2 .service-box .title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-2 .service-box .title {
    font-size: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-2 .service-box .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .services-wrapper-2 .service-box .title {
    font-size: 35px;
  }
}
.services-wrapper-2 .service-box .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: #FCF7F3;
  max-width: 370px;
  margin-top: 7px;
}
.dark .services-wrapper-2 .service-box .text {
  color: var(--black);
}

/* testimonial area style  */
.moving-testimonial .testimonial-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (max-width: 767px) {
  .moving-testimonial .testimonial-wrapper {
    height: auto;
  }
}

.testimonial-area {
  background-color: var(--primary);
}
.testimonial-area .section-title {
  max-width: 1000px;
  margin-left: auto;
  color: #FCF7F3;
}
.dark .testimonial-area .section-title {
  color: var(--black);
}
.testimonial-area .section-header {
  margin-top: 46px;
}
.testimonial-area .testimonial-wrapper-box {
  padding-top: 93px;
}
.testimonial-area .testimonial-wrapper {
  padding-top: 43px;
  padding-bottom: 43px;
  display: flex;
  gap: 0 0;
  align-items: flex-start;
}
@media only screen and (max-width: 1199px) {
  .testimonial-area .testimonial-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-area .testimonial-wrapper {
    display: grid;
    grid-template-columns: 1fr;
  }
}
.testimonial-area .testimonial-item {
  background-color: #1D1C1C;
  padding: 38px 45px 45px;
  min-width: 398px;
}
.dark .testimonial-area .testimonial-item {
  background-color: var(--black);
}
@media only screen and (max-width: 1919px) {
  .testimonial-area .testimonial-item {
    min-width: 358px;
  }
}
@media only screen and (max-width: 1919px) {
  .testimonial-area .testimonial-item {
    padding: 18px 30px 25px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-area .testimonial-item {
    padding: 18px 25px 25px;
  }
}
@media only screen and (max-width: 1919px) {
  .testimonial-area .testimonial-item:nth-child(n+5) {
    display: none;
  }
}
.testimonial-area .testimonial-item.light {
  background-color: #FCF7F3;
}
.dark .testimonial-area .testimonial-item.light {
  background-color: #EAE3DD;
}
.testimonial-area .testimonial-item.light .text {
  color: var(--black);
}
.testimonial-area .testimonial-item.light .name {
  color: var(--black);
}
.testimonial-area .testimonial-item.light .post {
  color: rgba(17, 17, 17, 0.4);
}
.testimonial-area .testimonial-item.light .icon {
  background-color: var(--black);
}
.testimonial-area .testimonial-item .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: #FCF7F3;
}
.testimonial-area .testimonial-item .name {
  font-size: 20px;
  font-weight: 310;
  line-height: 27px;
  letter-spacing: -0.07em;
  color: #FCF7F3;
}
.testimonial-area .testimonial-item .post {
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  color: rgba(252, 247, 243, 0.4);
  display: inline-block;
  margin-top: 1px;
}
.testimonial-area .testimonial-item .author {
  margin-top: 54px;
  display: grid;
  gap: 20px 30px;
  grid-template-columns: 1fr auto;
  align-items: center;
}
@media only screen and (max-width: 1919px) {
  .testimonial-area .testimonial-item .author {
    margin-top: 34px;
  }
}
.testimonial-area .testimonial-item .icon {
  width: 70px;
  height: 70px;
  background-color: #FCF7F3;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* client area 2 style  */
.client-area-2 {
  background-color: var(--primary);
  margin-bottom: -1px;
  position: relative;
  z-index: 1;
}
.client-area-2 .section-header .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  max-width: 340px;
  color: #FCF7F3;
  margin-inline: auto;
}
.dark .client-area-2 .section-header .text {
  color: var(--black);
}
.client-area-2 .clients-wrapper-box {
  margin-top: 63px;
}
@media only screen and (max-width: 1199px) {
  .client-area-2 .clients-wrapper-box {
    margin-top: 43px;
  }
}
.client-area-2 .clients-wrapper {
  display: flex;
  gap: 0;
  justify-content: center;
  flex-wrap: wrap;
}
.client-area-2 .clients-wrapper .client-slider-active .swiper-slide {
  width: auto;
}
.client-area-2 .clients-wrapper .client-slider-active .swiper-wrapper {
  transition-timing-function: linear !important;
}
.client-area-2 .client-box {
  border: 1px solid rgba(252, 247, 243, 0.1);
  border-radius: 70px;
  width: 215px;
  height: 140px;
  padding: 0 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.dark .client-area-2 .client-box {
  border-color: rgba(17, 17, 17, 0.1);
}
@media only screen and (max-width: 1919px) {
  .client-area-2 .client-box {
    width: 155px;
    height: 90px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area-2 .client-box {
    width: 135px;
    height: 70px;
  }
}

/* circular shape area style  */
.circular-shape-wrapper {
  height: 100vh;
  background-color: var(--primary);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.circular-shape-wrapper .shape-thumb img {
  transform: scale(1) rotate(0);
  opacity: 0.9;
}

/* award area style  */
.award-area {
  background-color: #FCF7F3;
  position: relative;
}
.dark .award-area {
  background-color: var(--black);
}
.award-area .section-title {
  max-width: 1190px;
}
.award-area .section-header {
  margin-top: 46px;
}
.award-area .awards-wrapper-box {
  margin-top: 93px;
  border-top: 1px solid var(--border);
  padding-top: 32px;
}
@media only screen and (max-width: 1919px) {
  .award-area .awards-wrapper-box {
    margin-top: 73px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area .awards-wrapper-box {
    margin-top: 53px;
  }
}
.award-area .awards-wrapper {
  max-width: 630px;
  margin-right: 270px;
  margin-left: auto;
}
@media only screen and (max-width: 1199px) {
  .award-area .awards-wrapper {
    margin-right: 0;
  }
}
.award-area .award-box {
  display: grid;
  gap: 20px 30px;
  grid-template-columns: 1fr 370px;
}
@media only screen and (max-width: 767px) {
  .award-area .award-box {
    grid-template-columns: 1fr 340px;
  }
}
@media (max-width: 575px) {
  .award-area .award-box {
    grid-template-columns: 1fr;
  }
}
.award-area .award-box:not(:first-child) {
  margin-top: 56px;
}
.award-area .award-box .award-list li {
  display: grid;
  gap: 10px 20px;
  grid-template-columns: auto auto;
  justify-content: space-between;
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
}
@media only screen and (max-width: 767px) {
  .award-area .award-box .award-list li {
    font-size: 18px;
  }
}
.award-area .award-box .category {
  font-size: 20px;
  font-weight: 400;
  line-height: 20px;
  color: var(--primary);
}
@media only screen and (max-width: 767px) {
  .award-area .award-box .category {
    font-size: 18px;
  }
}

/* cta area 2 style  */
.cta-area-2-inner {
  overflow: hidden;
}
.cta-area-2 .section-title {
  font-size: 200px;
  font-weight: 310;
  line-height: 0.85;
  letter-spacing: -0.09em;
  text-transform: uppercase;
  white-space: nowrap;
}
@media only screen and (max-width: 1919px) {
  .cta-area-2 .section-title {
    font-size: 140px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-2 .section-title {
    font-size: 100px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-2 .section-title {
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .cta-area-2 .section-title {
    font-size: 60px;
  }
}
@media (max-width: 575px) {
  .cta-area-2 .section-title {
    font-size: 40px;
  }
}
.cta-area-2 .section-title a {
  display: inline-flex;
  align-items: center;
}
.cta-area-2 .section-title .line {
  width: 1em;
  height: 0.1em;
  background-color: var(--primary);
  display: inline-block;
  align-self: center;
  margin-left: 0.3em;
  margin-right: 0.2em;
}
.cta-area-2 .section-header {
  margin-top: 70px;
  margin-bottom: 93px;
}
@media only screen and (max-width: 1919px) {
  .cta-area-2 .section-header {
    margin-top: 30px;
    margin-bottom: 73px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-2 .section-header {
    margin-top: 10px;
    margin-bottom: 53px;
  }
}
.cta-area-2 .section-header .title-wrapper {
  animation: 45s t-slide infinite linear;
}
.cta-area-2 .section-header .t-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  padding: 10px 20px;
  display: inline-block;
  background-color: var(--theme);
  color: var(--black);
  border-radius: 50px;
  position: absolute;
  top: 0;
  left: 0;
  margin: -25px 0 0 -65px;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}

/* marketing agency page css */
.body-marketing-agency {
  background-color: #FCF7F3;
}
.body-marketing-agency.dark {
  --border: rgba(252, 247, 243, 0.1);
}
@media (min-width: 1850px) {
  .body-marketing-agency .container.large {
    max-width: 1850px;
  }
}
.body-marketing-agency .section-title .mb-14 {
  transform: translate(0px, -14px);
  display: inline-block;
}
@media only screen and (max-width: 1199px) {
  .body-marketing-agency .section-title .mb-14 {
    display: block;
    transform: none;
  }
}
@media only screen and (max-width: 767px) {
  .body-marketing-agency .section-title .mb-14 {
    margin-top: 10px;
  }
}

/* hero area 3 style  */
.hero-area-3-inner {
  padding-top: 28px;
  padding-bottom: 100px;
}
.hero-area-3 .section-subtitle {
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
  text-transform: unset;
  display: inline-block;
  max-width: 231px;
  color: var(--primary);
  text-transform: math-auto;
}
.hero-area-3 .subtitle-wrapper {
  max-width: 1090px;
  margin-left: auto;
}
@media only screen and (max-width: 1919px) {
  .hero-area-3 .subtitle-wrapper {
    max-width: 790px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-3 .subtitle-wrapper {
    max-width: 690px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .subtitle-wrapper {
    max-width: 590px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-3 .subtitle-wrapper {
    margin-left: 0;
    margin-top: 70px;
  }
}
.hero-area-3 .section-title {
  font-size: 140px;
  font-weight: 310;
  line-height: 0.85;
  letter-spacing: -0.07em;
  max-width: 1240px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-3 .section-title {
    font-size: 100px;
    max-width: 940px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .section-title {
    font-size: 70px;
    max-width: 640px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-3 .section-title {
    font-size: 50px;
  }
}
.hero-area-3 .section-title .title-shape-1 {
  height: 96px;
  margin-right: 15px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-3 .section-title .title-shape-1 {
    height: 76px;
    margin-right: 10px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .section-title .title-shape-1 {
    height: 51px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-3 .section-title .title-shape-1 {
    height: 36px;
    margin-right: 5px;
  }
}
.hero-area-3 .section-title .title-shape-2 {
  height: 84px;
  margin-left: 5px;
  margin-top: -10px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-3 .section-title .title-shape-2 {
    height: 64px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .section-title .title-shape-2 {
    height: 44px;
    margin-top: -6px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-3 .section-title .title-shape-2 {
    height: 30px;
  }
}
.hero-area-3 .section-title .text-underline {
  color: rgba(17, 17, 17, 0.2);
  text-decoration-line: underline;
  text-decoration-thickness: 7px;
  text-underline-offset: 12px;
  position: relative;
  cursor: pointer;
}
.dark .hero-area-3 .section-title .text-underline {
  color: rgba(252, 247, 243, 0.2);
}
@media only screen and (max-width: 1919px) {
  .hero-area-3 .section-title .text-underline {
    text-decoration-thickness: 6px;
    text-underline-offset: 8px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .section-title .text-underline {
    text-decoration-thickness: 4px;
    text-underline-offset: 6px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-3 .section-title .text-underline {
    text-decoration-thickness: 3px;
    text-underline-offset: 4px;
  }
}
.hero-area-3 .section-title .text-underline .hover-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 200px;
  height: 250px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.hero-area-3 .section-title .text-underline .text-underline:hover .hover-image {
  opacity: 1;
}
.hero-area-3 .title-wrapper {
  margin-top: 43px;
}
@media only screen and (max-width: 991px) {
  .hero-area-3 .title-wrapper {
    margin-top: 23px;
  }
}
.hero-area-3 .social-links {
  display: flex;
  gap: 8px 20px;
  max-width: 270px;
  flex-wrap: wrap;
  align-self: flex-end;
}
@media only screen and (max-width: 991px) {
  .hero-area-3 .social-links {
    max-width: 100%;
  }
}
.hero-area-3 .social-links li {
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
  position: relative;
  text-decoration-style: solid;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: var(--primary);
}
.hero-area-3 .social-links li::before {
  width: 100%;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.hero-area-3 .social-links li:hover::before {
  width: 0;
}
.hero-area-3 .info-text {
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
  color: var(--primary);
  max-width: 140px;
}
.hero-area-3 .about-text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
  max-width: 410px;
}
@media only screen and (max-width: 991px) {
  .hero-area-3 .about-text {
    max-width: 100%;
  }
}
.hero-area-3 .section-content {
  display: grid;
  gap: 30px 50px;
  grid-template-columns: 1fr 310px 730px;
  margin-top: 88px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-3 .section-content {
    grid-template-columns: 1fr 310px 430px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-3 .section-content {
    grid-template-columns: 1fr 310px 330px;
    margin-top: 58px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .section-content {
    grid-template-columns: 1fr 210px 330px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-3 .section-content {
    grid-template-columns: 1fr;
  }
}
.hero-area-3 .section-content .btn-wrapper {
  margin-top: 56px;
}
@media only screen and (max-width: 1199px) {
  .hero-area-3 .section-content .btn-wrapper {
    margin-top: 36px;
  }
}

.text-underline {
  position: relative;
  cursor: pointer;
}

.image-hover {
  position: fixed;
  top: 0;
  left: 0;
  width: 200px;
  height: 250px;
  pointer-events: none;
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.3s ease, transform 0.3s ease;
}
@media (max-width: 768px) {
  .image-hover {
    display: none;
  }
}

.text-underline:hover .image-hover {
  opacity: 1;
  transform: scale(1);
}

/* service area 3 style  */
.service-area-3 .section-header {
  margin-top: 50px;
}
.service-area-3 .section-header .rr-btn-group.b {
  padding: 9px 18px !important;
}
.service-area-3 .services-wrapper-box {
  margin-top: 86px;
}
@media only screen and (max-width: 1919px) {
  .service-area-3 .services-wrapper-box {
    margin-top: 56px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-3 .services-wrapper-box {
    margin-top: 36px;
  }
}

.services-wrapper-3 {
  display: grid;
  gap: 30px 60px;
  grid-template-columns: repeat(4, 1fr);
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-3 {
    gap: 30px 40px;
  }
}
@media only screen and (max-width: 991px) {
  .services-wrapper-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .services-wrapper-3 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.services-wrapper-3 .service-box {
  display: grid;
  gap: 20px 30px;
  grid-template-columns: 110px 1fr;
  align-items: flex-start;
  padding-top: 12px;
  border-top: 1px solid var(--border);
  position: relative;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-3 .service-box {
    gap: 20px 20px;
    grid-template-columns: 70px 1fr;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-3 .service-box {
    grid-template-columns: 1fr;
  }
}
.services-wrapper-3 .service-box:hover .thumb img {
  transform: scale(1.1);
}
.services-wrapper-3 .service-box:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--border);
  top: 0;
  left: -30px;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-3 .service-box:before {
    left: -20px;
  }
}
.services-wrapper-3 .service-box .thumb {
  margin-top: 8px;
  border-radius: 25px;
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-3 .service-box .thumb {
    border-radius: 15px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-3 .service-box .thumb {
    max-width: 70px;
  }
}
.services-wrapper-3 .service-box .thumb img {
  width: 100%;
  transition: all 0.5s;
}
.services-wrapper-3 .service-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-3 .service-box .title {
    font-size: 24px;
  }
}
.services-wrapper-3 .service-box .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  margin-top: 18px;
}
.services-wrapper-3 .service-box .rr-btn-underline {
  text-transform: unset;
  font-size: 18px;
  color: var(--primary);
  margin-top: 50px;
  display: inline-block;
  position: relative;
}
.services-wrapper-3 .service-box .rr-btn-underline::before {
  content: "";
  width: 100%;
  height: 1px;
  background-color: currentColor;
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.4s;
}
.services-wrapper-3 .service-box .rr-btn-underline:hover::before {
  width: 0;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-3 .service-box .rr-btn-underline {
    margin-top: 30px;
  }
}

/* work area 3 style  */
.work-area-3 .section-title {
  max-width: 1060px;
}
@media only screen and (max-width: 1919px) {
  .work-area-3 .section-title {
    max-width: 860px;
  }
}
.work-area-3 .section-title .rr-btn-group:hover .c {
  transform: translate(-11px, 0px);
}
.work-area-3 .works-wrapper-box {
  margin-top: 86px;
}
@media only screen and (max-width: 1919px) {
  .work-area-3 .works-wrapper-box {
    margin-top: 56px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-3 .works-wrapper-box {
    margin-top: 36px;
  }
}

.works-wrapper-3 {
  display: grid;
  gap: 68px 20px;
  grid-template-columns: repeat(2, 1fr);
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-3 {
    gap: 48px 20px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-3 {
    gap: 38px 20px;
  }
}
@media (max-width: 575px) {
  .works-wrapper-3 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.works-wrapper-3 > * .image {
  transform-origin: bottom right;
}
.works-wrapper-3 > *:nth-child(2n) .image {
  transform-origin: bottom left;
}
.works-wrapper-3 .work-box .thumb:hover .t-btn {
  opacity: 1;
}
.works-wrapper-3 .work-box .thumb .image {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  transform: scale(0.9);
}
.works-wrapper-3 .work-box .thumb .image img {
  transform-origin: center;
}
.works-wrapper-3 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-3 .work-box .thumb .t-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  padding: 10px 20px;
  display: inline-block;
  background-color: white;
  color: var(--black);
  border-radius: 50px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  margin: -25px 0 0 -65px;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  pointer-events: none;
}
.works-wrapper-3 .work-box .content {
  margin-top: 14px;
}
.works-wrapper-3 .work-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.08em;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-3 .work-box .title {
    font-size: 22px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-3 .work-box .title {
    font-size: 20px;
  }
}
.works-wrapper-3 .work-box .meta {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 10px;
}
.works-wrapper-3 .work-box .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  display: flex;
  align-items: center;
}
.works-wrapper-3 .work-box .meta span:not(:first-child):before {
  content: "";
  width: 10px;
  height: 1px;
  background-color: currentColor;
  display: inline-block;
  margin-inline-end: 5px;
}

/* approach area style  */
.approach-area .section-header {
  margin-top: 43px;
}
.approach-area .section-title-wrapper {
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 1fr 1225px;
  align-items: flex-end;
}
@media only screen and (max-width: 1919px) {
  .approach-area .section-title-wrapper {
    grid-template-columns: 1fr 905px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area .section-title-wrapper {
    grid-template-columns: 1fr 675px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area .section-title-wrapper {
    grid-template-columns: 1fr 575px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.approach-area .section-subtitle {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .approach-area .section-subtitle {
    font-size: 24px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area .section-subtitle {
    font-size: 18px;
  }
  .approach-area .section-subtitle br {
    display: none;
  }
}
.approach-area .section-title {
  max-width: 1126px;
}
.approach-area .section-title span {
  color: rgba(17, 17, 17, 0.3);
}
.dark .approach-area .section-title span {
  color: rgba(252, 247, 243, 0.3);
}
.approach-area .approach-wrapper-box {
  margin-top: 94px;
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 1fr 1225px;
  align-items: flex-start;
  margin-bottom: 80px;
}
@media only screen and (max-width: 1919px) {
  .approach-area .approach-wrapper-box {
    grid-template-columns: 1fr 905px;
    margin-top: 64px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area .approach-wrapper-box {
    grid-template-columns: 1fr 675px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area .approach-wrapper-box {
    grid-template-columns: 1fr 575px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area .approach-wrapper-box {
    grid-template-columns: 1fr;
    margin-top: 44px;
  }
}
.approach-area .approach-wrapper-box .steps {
  font-family: var(--font_sequelsansromanbody);
  font-size: 265px;
  font-weight: 310;
  line-height: 0.65;
  letter-spacing: -0.07em;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .approach-area .approach-wrapper-box .steps {
    font-size: 205px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area .approach-wrapper-box .steps {
    font-size: 165px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area .approach-wrapper-box .steps {
    display: none;
  }
}
.approach-area .approach-box {
  display: grid;
  gap: 10px 50px;
  grid-template-columns: 60px 1fr 595px;
  align-items: flex-start;
  padding-top: 24px;
  padding-bottom: 24px;
  border-bottom: 1px dashed #878482;
}
.approach-area .approach-box:first-child {
  border-top: 1px dashed #878482;
}
@media only screen and (max-width: 1919px) {
  .approach-area .approach-box {
    grid-template-columns: 60px 1fr 395px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area .approach-box {
    grid-template-columns: 60px 1fr;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area .approach-box {
    gap: 10px 30px;
  }
}
@media (max-width: 575px) {
  .approach-area .approach-box {
    grid-template-columns: 30px 1fr;
  }
}
.approach-area .approach-box .number {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: var(--primary);
}
@media only screen and (max-width: 1399px) {
  .approach-area .approach-box .number {
    grid-row: span 2;
  }
}
.approach-area .approach-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 30px;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 1919px) {
  .approach-area .approach-box .title {
    font-size: 24px;
  }
}
.approach-area .approach-box .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
}

/* funfact area 2 style  */
.funfact-area-2 {
  background-color: var(--bg);
}
.funfact-area-2 .section-header {
  margin-top: 50px;
}
.funfact-area-2 .section-title {
  max-width: 820px;
  color: #FCF7F3;
}
@media only screen and (max-width: 1399px) {
  .funfact-area-2 .section-title {
    max-width: 500px;
  }
}
.funfact-area-2 .section-content {
  margin-top: 79px;
}
@media only screen and (max-width: 1919px) {
  .funfact-area-2 .section-content {
    margin-top: 59px;
  }
}
.funfact-area-2 .section-content .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--white-2);
  max-width: 410px;
}
.funfact-area-2 .section-content .year {
  font-family: var(--font_sequelsansromanbody);
  font-size: 580px;
  font-weight: 315;
  line-height: 0.8;
  letter-spacing: -0.1em;
  color: #FCF7F3;
  display: inline-block;
}
@media only screen and (max-width: 1919px) {
  .funfact-area-2 .section-content .year {
    font-size: 380px;
  }
}
@media only screen and (max-width: 1399px) {
  .funfact-area-2 .section-content .year {
    font-size: 320px;
  }
}
@media only screen and (max-width: 1199px) {
  .funfact-area-2 .section-content .year {
    font-size: 240px;
  }
}
@media only screen and (max-width: 991px) {
  .funfact-area-2 .section-content .year {
    font-size: 150px;
  }
}
@media (max-width: 575px) {
  .funfact-area-2 .section-content .year {
    font-size: 100px;
  }
}
.funfact-area-2 .section-content .info-text {
  margin-top: 49px;
  margin-left: 265px;
  display: grid;
  gap: 20px 90px;
  grid-template-columns: auto 1fr;
  align-items: flex-end;
}
@media only screen and (max-width: 1919px) {
  .funfact-area-2 .section-content .info-text {
    margin-top: 29px;
  }
}
@media only screen and (max-width: 1399px) {
  .funfact-area-2 .section-content .info-text {
    margin-left: 165px;
  }
}
@media only screen and (max-width: 991px) {
  .funfact-area-2 .section-content .info-text {
    margin-top: 19px;
  }
}
@media only screen and (max-width: 767px) {
  .funfact-area-2 .section-content .info-text {
    margin-left: 0;
  }
}
@media (max-width: 575px) {
  .funfact-area-2 .section-content .info-text {
    grid-template-columns: 1fr;
  }
}
.funfact-area-2 .section-content .info-text img {
  height: 43px;
}
@media only screen and (max-width: 1919px) {
  .funfact-area-2 .section-content .info-text img {
    height: 33px;
  }
}
@media only screen and (max-width: 1199px) {
  .funfact-area-2 .section-content .info-text img {
    height: 20px;
  }
}
@media (max-width: 575px) {
  .funfact-area-2 .section-content .info-text img {
    display: none;
  }
}
.funfact-area-2 .section-content .info-text .text {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: #FCF7F3;
  max-width: 273px;
  margin-top: 42px;
}
@media only screen and (max-width: 1919px) {
  .funfact-area-2 .section-content .info-text .text {
    font-size: 24px;
    max-width: 223px;
  }
}
@media only screen and (max-width: 1199px) {
  .funfact-area-2 .section-content .info-text .text {
    font-size: 18px;
    max-width: 173px;
    margin-top: 22px;
  }
}
.funfact-area-2 .section-content .text-wrapper {
  margin-left: 265px;
}
@media only screen and (max-width: 1399px) {
  .funfact-area-2 .section-content .text-wrapper {
    margin-left: 165px;
  }
}
@media only screen and (max-width: 767px) {
  .funfact-area-2 .section-content .text-wrapper {
    margin-left: 0;
  }
}
.funfact-area-2 .thumb {
  margin-top: 94px;
}
.funfact-area-2 .thumb img {
  width: 100%;
  transform: scale(0.67);
  transform-origin: top right;
}

/* client area 3 style  */
.client-area-3 {
  background-color: var(--bg);
}
.client-area-3 .section-header {
  margin-top: 69px;
}
.client-area-3 .section-header .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  max-width: 340px;
  color: #FCF7F3;
  margin-inline: auto;
}
.client-area-3 .clients-wrapper-box {
  margin-top: 63px;
  margin-bottom: 80px;
}
@media only screen and (max-width: 1199px) {
  .client-area-3 .clients-wrapper-box {
    margin-top: 43px;
  }
}
.client-area-3 .clients-wrapper {
  display: flex;
  gap: 0;
  justify-content: center;
  flex-wrap: wrap;
}
.client-area-3 .clients-wrapper .client-slider-active .swiper-slide {
  width: auto;
}
.client-area-3 .clients-wrapper .client-slider-active .swiper-wrapper {
  transition-timing-function: linear !important;
}
.client-area-3 .client-box {
  border: 1px solid rgba(252, 247, 243, 0.1);
  border-radius: 70px;
  width: 215px;
  height: 140px;
  padding: 0 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
@media only screen and (max-width: 1919px) {
  .client-area-3 .client-box {
    width: 155px;
    height: 90px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area-3 .client-box {
    width: 135px;
    height: 70px;
  }
}

/* blog area style  */
.blog-area .section-title .rr-btn-group:hover .c {
  transform: translate(-11px, 0px);
}
.blog-area .section-title {
  max-width: 710px;
}
.blog-area .blogs-wrapper-box {
  margin-top: 86px;
}
@media only screen and (max-width: 1919px) {
  .blog-area .blogs-wrapper-box {
    margin-top: 56px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area .blogs-wrapper-box {
    margin-top: 36px;
  }
}
.blog-area .blogs-wrapper {
  display: grid;
  gap: 76px 60px;
  grid-template-columns: repeat(4, 1fr);
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .blog-area .blogs-wrapper {
    gap: 46px 40px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area .blogs-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .blog-area .blogs-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.blog-area .blog {
  position: relative;
}
.blog-area .blog:hover .thumb img {
  transform: scale(1.1);
}
.blog-area .blog:hover .title .arrow {
  background-color: var(--primary);
}
.blog-area .blog:hover .title .arrow svg {
  transform: rotate(60deg);
}
.blog-area .blog:hover .title .arrow svg * {
  fill: var(--white);
}
.dark .blog-area .blog:hover .title .arrow svg * {
  fill: var(--black);
}
.blog-area .blog:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--border);
  top: 0;
  left: -30px;
}
@media only screen and (max-width: 1919px) {
  .blog-area .blog:before {
    left: -20px;
  }
}
.blog-area .blog .thumb {
  overflow: hidden;
}
.blog-area .blog .thumb img {
  width: 100%;
  transition: all 0.5s;
}
.blog-area .blog .content {
  margin-top: 24px;
}
@media only screen and (max-width: 1199px) {
  .blog-area .blog .content {
    margin-top: 14px;
  }
}
.blog-area .blog .title {
  font-size: 36px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  display: inline;
}
@media only screen and (max-width: 1919px) {
  .blog-area .blog .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area .blog .title {
    font-size: 22px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area .blog .title {
    font-size: 20px;
  }
}
.blog-area .blog .title .arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  transition: all 0.3s;
  border-radius: 50%;
  border: 2px solid var(--primary);
  transform: translate(0px, -1px);
  margin-left: 5px;
}
@media only screen and (max-width: 1919px) {
  .blog-area .blog .title .arrow {
    width: 20px;
    height: 20px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area .blog .title .arrow {
    width: 17px;
    height: 17px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area .blog .title .arrow {
    width: 15px;
    height: 15px;
    border-width: 1px;
  }
}
.blog-area .blog .title .arrow svg {
  transition: all 0.3s;
  width: 13px;
}
@media only screen and (max-width: 1919px) {
  .blog-area .blog .title .arrow svg {
    width: 10px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area .blog .title .arrow svg {
    width: 7px;
  }
}
.blog-area .blog .title .arrow svg * {
  fill: var(--primary);
}
.blog-area .blog .meta {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 14px;
}
@media only screen and (max-width: 1199px) {
  .blog-area .blog .meta {
    margin-top: 9px;
  }
}
.blog-area .blog .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: var(--secondary);
}
.blog-area .blog .meta span.has-left-line {
  padding-inline-start: 15px;
}
.blog-area .blog .meta span.has-left-line:before {
  width: 10px;
}
.blog-area .blog .meta .name span {
  font-weight: 500;
  color: var(--primary);
}

/* cta area 3 style  */
.cta-area-3 .section-header {
  margin-top: 50px;
  margin-left: 925px;
  margin-bottom: 86px;
}
@media only screen and (max-width: 1919px) {
  .cta-area-3 .section-header {
    margin-bottom: 56px;
    margin-left: 555px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-3 .section-header {
    margin-left: 475px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-3 .section-header {
    margin-bottom: 36px;
    margin-left: 325px;
  }
}
@media only screen and (max-width: 991px) {
  .cta-area-3 .section-header {
    margin-left: 0;
  }
}
.cta-area-3 .section-header .rr-btn-group:hover .c {
  transform: translate(-11px, 0px);
}
.cta-area-3 .section-title {
  max-width: 680px;
}
@media only screen and (max-width: 1919px) {
  .cta-area-3 .section-title {
    max-width: 540px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-3 .section-title {
    max-width: 410px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-3 .section-title {
    max-width: 340px;
  }
}

/* startup agency page css */
.body-startup-agency {
  background-color: #FFFFFF;
}
@media (min-width: 1650px) {
  .body-startup-agency .container.large {
    max-width: 1650px;
  }
}
.body-startup-agency .section-subtitle {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  text-transform: unset;
  display: inline-block;
  color: var(--primary);
  padding-bottom: 10px;
  position: relative;
}
.body-startup-agency .section-subtitle svg {
  position: absolute;
  bottom: 0;
  width: 100% !important;
  left: 0;
  height: 7px;
}
@-webkit-keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 195.8011322021px;
    stroke-dasharray: 195.8011322021px;
  }
  20% {
    stroke-dashoffset: 391.6022644043px;
    stroke-dasharray: 195.8011322021px;
  }
  100% {
    stroke-dashoffset: 391.6022644043px;
    stroke-dasharray: 195.8011322021px;
  }
}
@keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 195.8011322021px;
    stroke-dasharray: 195.8011322021px;
  }
  20% {
    stroke-dashoffset: 391.6022644043px;
    stroke-dasharray: 195.8011322021px;
  }
  100% {
    stroke-dashoffset: 391.6022644043px;
    stroke-dasharray: 195.8011322021px;
  }
}
.body-startup-agency .section-subtitle .svg-elem-1 {
  -webkit-animation: animate-svg-stroke-1 5s ease-in-out 0s both infinite;
  animation: animate-svg-stroke-1 5s ease-in-out 0s both infinite;
}
.body-startup-agency .rr-btn-underline {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  text-transform: unset;
  gap: 5px;
}
.body-startup-agency .rr-btn-underline::before {
  height: 1px;
}
.body-startup-agency .rr-btn-underline .icon {
  width: 13px;
}
.body-startup-agency .header-area-5 .side-toggle {
  background-color: rgb(243, 243, 243);
}

/* hero area style  */
.hero-area-5 .section-content-wrapper {
  margin-top: 16px;
  display: grid;
  gap: 40px 110px;
  grid-template-columns: 715px 1fr;
}
@media only screen and (max-width: 1919px) {
  .hero-area-5 .section-content-wrapper {
    gap: 40px 80px;
    grid-template-columns: 515px 1fr;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-5 .section-content-wrapper {
    gap: 40px 60px;
    grid-template-columns: 425px 1fr;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-5 .section-content-wrapper {
    grid-template-columns: 455px 1fr;
    margin-top: 26px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-5 .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.hero-area-5 .hero-video {
  margin-top: 14px;
  border-radius: 15px;
  overflow: hidden;
}
.hero-area-5 .hero-video video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hero-area-5 .section-title {
  font-size: 80px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.05em;
  max-width: 575px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-5 .section-title {
    font-size: 70px;
    max-width: 505px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-5 .section-title {
    font-size: 60px;
    max-width: 455px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-5 .section-title {
    font-size: 50px;
    max-width: 375px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-5 .section-title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-5 .section-title {
    font-size: 35px;
  }
}
.hero-area-5 .section-content .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
}
.hero-area-5 .section-content .text-btn-wrapper {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  margin-top: 43px;
  display: grid;
  grid-template-columns: 330px 1fr;
}
@media only screen and (max-width: 1199px) {
  .hero-area-5 .section-content .text-btn-wrapper {
    grid-template-columns: 1fr;
  }
}
.hero-area-5 .section-content .text-wrapper {
  margin-top: 62px;
  margin-bottom: 62px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-5 .section-content .text-wrapper {
    margin-top: 42px;
    margin-bottom: 42px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-5 .section-content .text-wrapper {
    margin-top: 32px;
    margin-bottom: 32px;
  }
}
.hero-area-5 .section-content .btn-wrapper {
  border-left: 1px solid var(--border);
  margin-left: 74px;
  padding-left: 28px;
  padding-top: 90px;
  padding-bottom: 68px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-5 .section-content .btn-wrapper {
    margin-left: 44px;
    padding-top: 70px;
    padding-bottom: 48px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-5 .section-content .btn-wrapper {
    margin-left: 0;
    padding-top: 0;
    padding-bottom: 38px;
    border-left: 0;
    padding-left: 0;
  }
}

/* work area 4 style  */
.work-area-4 .section-header {
  border-top: 1px solid var(--border);
  padding-top: 35px;
}
.work-area-4 .section-header .btn-wrapper {
  margin-top: 44px;
}
@media only screen and (max-width: 1919px) {
  .work-area-4 .section-header .btn-wrapper {
    margin-top: 34px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-4 .section-header .btn-wrapper {
    margin-top: 24px;
  }
}
.work-area-4 .section-header .subtitle-wrapper {
  margin-top: 9px;
}
.work-area-4 .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1015px;
}
@media only screen and (max-width: 1919px) {
  .work-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 815px;
  }
}
@media only screen and (max-width: 1399px) {
  .work-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 700px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 600px;
  }
}
@media only screen and (max-width: 991px) {
  .work-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 767px) {
  .work-area-4 .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.work-area-4 .section-title {
  max-width: 700px;
}
@media only screen and (max-width: 1919px) {
  .work-area-4 .section-title {
    max-width: 600px;
  }
}
@media only screen and (max-width: 1399px) {
  .work-area-4 .section-title {
    max-width: 450px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-4 .section-title {
    max-width: 350px;
  }
}
@media only screen and (max-width: 767px) {
  .work-area-4 .section-title {
    max-width: 100%;
  }
}
.work-area-4 .works-wrapper-box {
  margin-top: 29px;
}
@media only screen and (max-width: 1399px) {
  .work-area-4 .works-wrapper-box {
    margin-top: 9px;
  }
}

.works-wrapper-4 {
  display: grid;
  gap: 85px 10px;
  grid-template-columns: repeat(2, 1fr);
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-4 {
    gap: 65px 10px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-4 {
    gap: 45px 10px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-4 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.works-wrapper-4 > * .image {
  transform-origin: bottom right;
}
.works-wrapper-4 > *:nth-child(2n) .image {
  transform-origin: bottom left;
}
.works-wrapper-4 .work-box .thumb {
  border-radius: 15px;
  overflow: hidden;
}
.works-wrapper-4 .work-box .thumb .image {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  transform: scale(0.9);
}
.works-wrapper-4 .work-box .thumb .image img {
  transform-origin: center;
}
.works-wrapper-4 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-4 .work-box .content {
  margin-top: 24px;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-4 .work-box .content {
    margin-top: 14px;
  }
}
.works-wrapper-4 .work-box .title {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-4 .work-box .title {
    font-size: 18px;
  }
}
.works-wrapper-4 .work-box .tag {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
  display: block;
  font-family: var(--font_bdogrotesk);
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-4 .work-box .tag {
    font-size: 18px;
  }
}
.works-wrapper-4 .work-box .date {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
  display: block;
  font-family: var(--font_bdogrotesk);
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-4 .work-box .date {
    font-size: 18px;
  }
}

/* marquee text area style  */
.marquee-text-area {
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.marquee-text-area .section-title {
  font-size: 280px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: -0.07em;
  white-space: nowrap;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1919px) {
  .marquee-text-area .section-title {
    font-size: 210px;
    margin-bottom: 40px;
  }
}
@media only screen and (max-width: 1399px) {
  .marquee-text-area .section-title {
    font-size: 200px;
  }
}
@media only screen and (max-width: 1199px) {
  .marquee-text-area .section-title {
    font-size: 150px;
    margin-bottom: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .marquee-text-area .section-title {
    font-size: 110px;
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .marquee-text-area .section-title {
    font-size: 90px;
  }
}
@media (max-width: 575px) {
  .marquee-text-area .section-title {
    font-size: 60px;
    margin-bottom: 10px;
  }
}
.marquee-text-area .moving-text {
  width: 100%;
}

/* about area 4 style  */
.about-area-4 .section-header {
  border-top: 1px solid var(--border);
  padding-top: 35px;
}
.about-area-4 .section-header .btn-wrapper {
  display: flex;
  gap: 20px 25px;
  align-items: center;
  margin-top: 73px;
  flex-wrap: wrap;
}
@media only screen and (max-width: 1919px) {
  .about-area-4 .section-header .btn-wrapper {
    margin-top: 53px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-4 .section-header .btn-wrapper {
    margin-top: 43px;
  }
}
.about-area-4 .section-header .subtitle-wrapper {
  margin-top: 9px;
}
.about-area-4 .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1015px;
}
@media only screen and (max-width: 1919px) {
  .about-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 815px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 700px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 600px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-4 .section-title-wrapper {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area-4 .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.about-area-4 .section-title {
  max-width: 885px;
}
@media only screen and (max-width: 1919px) {
  .about-area-4 .section-title {
    max-width: 785px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-4 .section-title {
    max-width: 585px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-4 .section-title {
    max-width: 455px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area-4 .section-title {
    max-width: 100%;
  }
}
.about-area-4 .thumb {
  margin-top: 80px;
}
@media only screen and (max-width: 1919px) {
  .about-area-4 .thumb {
    margin-top: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-4 .thumb {
    margin-top: 50px;
  }
}
.about-area-4 .thumb img {
  width: 100%;
}

/* service area 5 style  */
.service-area-5 .section-header {
  border-top: 1px solid var(--border);
  padding-top: 35px;
}
.service-area-5 .section-header .subtitle-wrapper {
  margin-top: 9px;
}
.service-area-5 .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1015px;
}
@media only screen and (max-width: 1919px) {
  .service-area-5 .section-title-wrapper {
    grid-template-columns: 1fr 815px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-5 .section-title-wrapper {
    grid-template-columns: 1fr 700px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-area-5 .section-title-wrapper {
    grid-template-columns: 1fr 600px;
  }
}
@media only screen and (max-width: 991px) {
  .service-area-5 .section-title-wrapper {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 767px) {
  .service-area-5 .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.service-area-5 .section-title {
  max-width: 483px;
}
@media only screen and (max-width: 1919px) {
  .service-area-5 .section-title {
    max-width: 383px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-5 .section-title {
    max-width: 300px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-area-5 .section-title {
    max-width: 250px;
  }
}
@media only screen and (max-width: 767px) {
  .service-area-5 .section-title {
    max-width: 100%;
  }
}
.service-area-5 .services-wrapper-box {
  margin-top: 87px;
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1015px;
}
@media only screen and (max-width: 1919px) {
  .service-area-5 .services-wrapper-box {
    grid-template-columns: 1fr 815px;
    margin-top: 57px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-5 .services-wrapper-box {
    grid-template-columns: 1fr 700px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-area-5 .services-wrapper-box {
    grid-template-columns: 1fr 600px;
    margin-top: 47px;
  }
}
@media only screen and (max-width: 991px) {
  .service-area-5 .services-wrapper-box {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 991px) {
  .service-area-5 .services-wrapper-box {
    grid-template-columns: 1fr;
  }
}
.service-area-5 .services-wrapper-box .info-text {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  max-width: 280px;
}
@media only screen and (max-width: 1399px) {
  .service-area-5 .services-wrapper-box .info-text {
    font-size: 16px;
  }
}

.services-wrapper-5 {
  border-top: 1px solid var(--border);
  margin-top: 6px;
}
.services-wrapper-5 .service-box {
  border-bottom: 1px solid var(--border);
  padding-top: 21px;
  padding-bottom: 29px;
  display: grid;
  gap: 15px 30px;
  grid-template-columns: 120px 1fr 295px;
  pointer-events: auto;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-5 .service-box {
    padding-top: 11px;
    padding-bottom: 19px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-5 .service-box {
    grid-template-columns: 80px 1fr 225px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-5 .service-box {
    grid-template-columns: 60px 1fr 205px;
  }
}
@media (max-width: 575px) {
  .services-wrapper-5 .service-box {
    grid-template-columns: 1fr;
  }
}
.services-wrapper-5 .service-box:hover .thumb img {
  max-width: 100%;
}
.services-wrapper-5 .service-box:hover .text {
  opacity: 1;
  height: 100%;
}
.services-wrapper-5 .service-box .number {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-5 .service-box .number {
    font-size: 16px;
  }
}
.services-wrapper-5 .service-box .title {
  font-size: 36px;
  font-weight: 400;
  line-height: 1.11;
  letter-spacing: -0.05em;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-5 .service-box .title {
    font-size: 32px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-5 .service-box .title {
    font-size: 24px;
  }
}
.services-wrapper-5 .service-box .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  margin-top: 28px;
  opacity: 0;
  max-height: 0px;
  transition: opacity 0.3s, height 0.5s;
  max-width: 370px;
}
@media only screen and (max-width: 1919px) {
  .services-wrapper-5 .service-box .text {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 1399px) {
  .services-wrapper-5 .service-box .text {
    font-size: 16px;
  }
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-5 .service-box .text {
    opacity: 1;
    max-height: 100%;
  }
}
.services-wrapper-5 .service-box .count {
  margin-top: 2px;
}
.services-wrapper-5 .service-box .thumb {
  margin-top: 8px;
  text-align: right;
}
@media (max-width: 575px) {
  .services-wrapper-5 .service-box .thumb {
    text-align: left;
  }
}
.services-wrapper-5 .service-box .thumb img {
  border-radius: 15px;
  width: 100%;
  max-width: 165px;
  height: auto;
  transition: all 0.5s;
}
@media only screen and (max-width: 1199px) {
  .services-wrapper-5 .service-box .thumb img {
    width: 100%;
  }
}

/* cta area 4 style  */
.cta-area-4 .section-header {
  margin-top: 78px;
}
@media only screen and (max-width: 1919px) {
  .cta-area-4 .section-header {
    margin-top: 58px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-4 .section-header {
    margin-top: 38px;
  }
}
.cta-area-4 .section-title {
  font-size: 140px;
  font-weight: 400;
  line-height: 0.96;
  letter-spacing: -0.05em;
  padding-bottom: 34px;
  position: relative;
  display: inline-flex;
}
@media only screen and (max-width: 1919px) {
  .cta-area-4 .section-title {
    font-size: 120px;
    padding-bottom: 24px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-4 .section-title {
    font-size: 110px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-4 .section-title {
    font-size: 90px;
    padding-bottom: 14px;
  }
}
@media only screen and (max-width: 991px) {
  .cta-area-4 .section-title {
    font-size: 60px;
  }
}
@media (max-width: 575px) {
  .cta-area-4 .section-title {
    font-size: 40px;
  }
}
.cta-area-4 .section-title:hover::before {
  width: 0;
}
.cta-area-4 .section-title:hover .icon .first {
  transform: translate(100%, -100%);
}
.cta-area-4 .section-title:hover .icon .second {
  transform: translate(0%, 0%);
}
.cta-area-4 .section-title::before {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  bottom: 0px;
  width: 100%;
  height: 5px;
  background-color: currentColor;
  transition: 0.3s;
}
@media only screen and (max-width: 1199px) {
  .cta-area-4 .section-title::before {
    height: 3px;
  }
}
.cta-area-4 .section-title .icon {
  --white-space: 0.07em;
  margin-left: 24px;
  display: inline-block;
  line-height: 0;
  position: relative;
  overflow: hidden;
  transform: translate(0, var(--white-space));
}
@media only screen and (max-width: 1199px) {
  .cta-area-4 .section-title .icon {
    margin-left: 19px;
  }
}
@media (max-width: 575px) {
  .cta-area-4 .section-title .icon {
    margin-left: 14px;
  }
}
.cta-area-4 .section-title .icon .first {
  transition: all 0.3s;
  width: 0.72em;
  box-sizing: content-box;
  padding: var(--white-space);
}
.cta-area-4 .section-title .icon .second {
  position: absolute;
  bottom: 0;
  left: 0;
  transform: translate(-100%, 100%);
  transition: all 0.3s;
  width: 0.72em;
  box-sizing: content-box;
  padding: var(--white-space);
}
.cta-area-4 .section-title br {
  display: block;
}

/* reveal animation style  */
.img_anim_reveal {
  visibility: hidden;
  overflow: hidden;
}
.img_anim_reveal img {
  object-fit: cover;
  transform-origin: top;
}

/* portfolio agency page css */
.body-portfolio-agency {
  background-color: #F8F8F8;
}
@media (min-width: 1870px) {
  .body-portfolio-agency .container.large {
    max-width: 1870px;
  }
}

.hero-area-6 {
  overflow-x: clip;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hero-area-6 .section-content-wrapper {
  display: grid;
  gap: 40px 50px;
  grid-template-columns: 1220px 1fr;
}
@media only screen and (max-width: 1919px) {
  .hero-area-6 .section-content-wrapper {
    grid-template-columns: 850px 1fr;
    gap: 40px 30px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-6 .section-content-wrapper {
    grid-template-columns: 750px 1fr;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-6 .section-content-wrapper {
    grid-template-columns: 600px 1fr;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-6 .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.hero-area-6 .hero-video-wrapper {
  padding-right: 50px;
  padding-top: 30px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-6 .hero-video-wrapper {
    padding-right: 30px;
    padding-bottom: 20px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-6 .hero-video-wrapper {
    padding-left: 30px;
    padding-bottom: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-6 .hero-video-wrapper {
    padding-top: 0;
  }
}
.hero-area-6 .hero-video-wrapper .text {
  font-size: 20px;
  color: var(--primary);
  max-width: 330px;
  line-height: 28px;
  padding-top: 70px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-6 .hero-video-wrapper .text {
    padding-top: 50px;
    max-width: 100%;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-6 .hero-video-wrapper .text {
    padding-top: 50px;
    max-width: 100%;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-6 .hero-video-wrapper .text {
    font-size: 18px;
    padding-top: 20px;
    line-height: 25px;
    max-width: 100%;
  }
}
.hero-area-6 .hero-video-wrapper .hero-video {
  border-radius: 15px;
  overflow: hidden;
}
.hero-area-6 .hero-video-wrapper .hero-video video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hero-area-6-wrapper {
  margin-top: 230px;
  position: relative;
}
@media only screen and (max-width: 1919px) {
  .hero-area-6-wrapper {
    margin-top: 200px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-6-wrapper {
    margin-top: 170px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-6-wrapper {
    margin-top: 150px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-6-wrapper {
    margin-top: 120px;
  }
}
.hero-area-6-wrapper::before {
  content: "";
  position: absolute;
  left: 0px;
  top: -35px;
  width: 1px;
  height: calc(100% + 70px);
  background-color: var(--border);
}
@media only screen and (max-width: 767px) {
  .hero-area-6-wrapper::before {
    top: -15px;
    height: calc(100% + 30px);
  }
}
.hero-area-6-wrapper::after {
  content: "";
  position: absolute;
  right: 0px;
  top: -35px;
  width: 1px;
  height: calc(100% + 70px);
  background-color: var(--border);
}
@media only screen and (max-width: 767px) {
  .hero-area-6-wrapper::after {
    top: -15px;
    height: calc(100% + 30px);
  }
}
.hero-area-6-wrapper__line {
  position: relative;
}
.hero-area-6-wrapper__line::before {
  content: "";
  position: absolute;
  top: 0px;
  left: -35px;
  height: 1px;
  width: calc(100% + 70px);
  background-color: var(--border);
}
@media only screen and (max-width: 767px) {
  .hero-area-6-wrapper__line::before {
    left: -15px;
    width: calc(100% + 30px);
  }
}
.hero-area-6-wrapper__line::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: -35px;
  height: 1px;
  width: calc(100% + 70px);
  background-color: var(--border);
}
@media only screen and (max-width: 767px) {
  .hero-area-6-wrapper__line::after {
    left: -15px;
    width: calc(100% + 30px);
  }
}
.hero-area-6 .section-content {
  position: relative;
}
@media only screen and (max-width: 991px) {
  .hero-area-6 .section-content {
    overflow: hidden;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-6 .section-content {
    padding-top: 30px;
  }
}
.hero-area-6 .section-content::before {
  content: "";
  position: absolute;
  right: 0;
  top: -30px;
  width: 1px;
  height: calc(100% + 60px);
  background-color: var(--border);
}
@media only screen and (max-width: 991px) {
  .hero-area-6 .section-content::before {
    display: none;
  }
}
.hero-area-6 .section-title {
  font-size: 220px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -15.4px;
  text-transform: uppercase;
}
@media only screen and (max-width: 1919px) {
  .hero-area-6 .section-title {
    font-size: 160px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-6 .section-title {
    font-size: 130px;
    letter-spacing: -10px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-6 .section-title {
    font-size: 105px;
    letter-spacing: -5px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-6 .section-title {
    font-size: 90px;
    letter-spacing: -5px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-6 .section-title {
    font-size: 60px;
    letter-spacing: 0;
  }
}
@media (max-width: 575px) {
  .hero-area-6 .section-title {
    font-size: 45px;
    letter-spacing: 0;
  }
}
.hero-area-6 .section-title > * {
  display: block;
  font-weight: 600;
  padding-left: 50px;
  padding-right: 50px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-6 .section-title > * {
    padding-left: 30px;
    padding-right: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-6 .section-title > * {
    padding-left: 25px;
    padding-right: 10px;
  }
}
.hero-area-6 .section-title > *:last-child {
  font-style: italic;
  font-family: var(--font_tartuffo);
  text-align: right;
  margin-right: 30px;
  position: relative;
}
@media (max-width: 575px) {
  .hero-area-6 .section-title > *:last-child {
    text-align: left;
  }
}
.hero-area-6 .section-title > *:nth-child(2) {
  position: relative;
}
.hero-area-6 .section-title > *:nth-child(2)::before {
  content: "";
  position: absolute;
  top: 5px;
  left: 0px;
  height: 1px;
  width: calc(100% + 0px);
  background-color: var(--border);
}
@media only screen and (max-width: 767px) {
  .hero-area-6 .section-title > *:nth-child(2)::before {
    display: none;
  }
}
.hero-area-6 .section-title > *:nth-child(2)::after {
  content: "";
  position: absolute;
  bottom: -7px;
  left: 0px;
  height: 1px;
  width: calc(100% + 0px);
  background-color: var(--border);
}
@media only screen and (max-width: 767px) {
  .hero-area-6 .section-title > *:nth-child(2)::after {
    display: none;
  }
}
.hero-area-6 .section-title .plus {
  font-weight: 600;
  font-family: var(--font_bdogrotesk);
  font-style: normal;
  padding-right: 40px;
}
@media only screen and (max-width: 767px) {
  .hero-area-6 .section-title .plus {
    padding-right: 20px;
  }
}
@media (max-width: 575px) {
  .hero-area-6 .section-title .plus {
    padding-right: 0;
  }
}

/* work area 4 style  */
.work-area-6-inner {
  padding-top: 150px;
}
@media only screen and (max-width: 991px) {
  .work-area-6-inner {
    padding-top: 120px;
  }
}
@media only screen and (max-width: 767px) {
  .work-area-6-inner {
    padding-top: 100px;
  }
}
.work-area-6 .works-wrapper-box {
  margin-top: 29px;
}
@media only screen and (max-width: 1399px) {
  .work-area-6 .works-wrapper-box {
    margin-top: 9px;
  }
}

.works-wrapper-6 {
  display: grid;
  gap: 85px 10px;
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-6 {
    gap: 65px 10px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-6 {
    gap: 45px 10px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-6 {
    grid-template-columns: repeat(1, 1fr);
  }
  .works-wrapper-6 > * {
    grid-column: span 1 !important;
  }
}
.works-wrapper-6 > *:nth-child(1) {
  grid-column: span 2;
}
.works-wrapper-6 > *:nth-child(2) {
  grid-column: span 2;
}
.works-wrapper-6 > *:nth-child(3) {
  grid-column: span 2;
}
.works-wrapper-6 > *:nth-child(4) {
  grid-column: span 2;
}
.works-wrapper-6 > *:nth-child(5) {
  grid-column: span 2;
}
@media only screen and (max-width: 991px) {
  .works-wrapper-6 > *:nth-child(6) {
    grid-column: span 2;
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-6 > *:nth-child(7) {
    grid-column: span 2;
  }
}
.works-wrapper-6 > *:nth-child(8) {
  grid-column: span 4;
}
.works-wrapper-6 > *:nth-child(9) {
  grid-column: span 2;
}
.works-wrapper-6 > *:nth-child(10) {
  grid-column: span 2;
}
.works-wrapper-6 > *:nth-child(11) {
  grid-column: span 2;
}
@media only screen and (max-width: 991px) {
  .works-wrapper-6 > *:nth-child(12) {
    grid-column: span 2;
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-6 > *:nth-child(13) {
    grid-column: span 2;
  }
}
.works-wrapper-6 .work-box .thumb {
  border-radius: 15px;
  overflow: hidden;
}
.works-wrapper-6 .work-box .thumb .image {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  transform: scale(0.9);
}
.works-wrapper-6 .work-box .thumb .image img {
  transform-origin: center;
}
.works-wrapper-6 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-6 .work-box .content {
  margin-top: 24px;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-6 .work-box .content {
    margin-top: 14px;
  }
}
.works-wrapper-6 .work-box .title {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-6 .work-box .title {
    font-size: 18px;
  }
}
.works-wrapper-6 .work-box .tag {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
  display: block;
  font-family: var(--font_bdogrotesk);
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-6 .work-box .tag {
    font-size: 18px;
  }
}
.works-wrapper-6 .work-box .date {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
  display: block;
  font-family: var(--font_bdogrotesk);
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-6 .work-box .date {
    font-size: 18px;
  }
}

/* portfolio page css */
.body-portfolio-agency .header-area-7 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  right: 0;
}

.portfolio {
  width: 100vw;
  height: 100vh;
}
.portfolio__item {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: 0.8s cubic-bezier(0.37, 0.23, 0, 0.96);
}
.portfolio__content {
  z-index: 9;
  left: 50px;
  bottom: 200px;
  position: absolute;
}
@media (max-width: 575px) {
  .portfolio__content {
    top: 120px;
    left: 30px;
  }
}
.portfolio__content-title {
  opacity: 0;
  font-size: 100px;
  line-height: 1.05;
  color: var(--white);
  transform: translateY(-130px);
}
.portfolio__content-title a:hover {
  color: var(--white);
}
@media (max-width: 575px) {
  .portfolio__content-title {
    font-size: 60px;
  }
}
.portfolio__list {
  opacity: 0;
  display: flex;
  margin-top: 40px;
  align-items: center;
  transform: translateY(-150px);
}
.portfolio__list li a {
  font-size: 14px;
  border-radius: 20px;
  padding: 10px 17px;
  border-radius: 20px;
  color: var(--white);
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.portfolio .swiper-slide {
  overflow: hidden;
}
.portfolio-activ {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.portfolio .slide-inner {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: left;
}
.portfolio .swiper-slide-active .portfolio__item {
  animation-name: qodef-animate-slide-out;
  animation-duration: 1.3s;
  animation-fill-mode: forwards;
}
.portfolio .swiper-slide-active .portfolio__content-title {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2200ms ease;
}
.portfolio .swiper-slide-active .portfolio__list {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2000ms ease;
}
.portfolio__slider__arrow {
  gap: 40px;
  right: 50px;
  bottom: 50px;
  z-index: 99;
  display: flex;
  position: absolute;
  align-items: center;
}
@media only screen and (max-width: 991px) {
  .portfolio__slider__arrow {
    gap: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio__slider__arrow {
    left: 50px;
  }
}
@media (max-width: 575px) {
  .portfolio__slider__arrow {
    left: 30px;
  }
}
.portfolio__slider__arrow-prev, .portfolio__slider__arrow-next {
  gap: 8px;
  display: flex;
  font-size: 14px;
  font-weight: 600;
  align-items: center;
  color: var(--white);
}
.portfolio .portfolio-pagination {
  position: absolute;
  bottom: 50px;
  left: 50px;
  z-index: 9;
  display: inline-block;
}
@media (max-width: 575px) {
  .portfolio .portfolio-pagination {
    left: 30px;
  }
}
.portfolio .swiper-pagination-bullet {
  width: 150px;
  height: 10px;
  display: inline-block;
  margin: 0 5px;
  overflow: hidden;
  transition: 0.4s;
  position: relative;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
@media only screen and (max-width: 1199px) {
  .portfolio .swiper-pagination-bullet {
    width: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .portfolio .swiper-pagination-bullet {
    width: 90px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio .swiper-pagination-bullet {
    width: 70px;
    bottom: 45px;
  }
}
@media (max-width: 575px) {
  .portfolio .swiper-pagination-bullet {
    width: 40px;
  }
}
.portfolio .swiper-pagination-bullet::before {
  content: "";
  width: 150px;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 9;
  left: 0;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
.portfolio .swiper-pagination-bullet::after {
  content: "";
  width: 0;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 8;
  left: 0;
  background-color: white;
}
.portfolio .swiper-pagination-bullet-active::after {
  opacity: 1;
  width: 100%;
}

.portfolio-2 .line-effect {
  top: 0;
  left: 0;
  gap: 10px;
  width: 100vh;
  height: 50vw;
  display: flex;
  position: absolute;
  flex-direction: column;
  transition: 0.8s ease-in-out;
  transform-origin: bottom left;
  transform: translateY(-100%) rotate(90deg);
}
@media (max-width: 575px) {
  .portfolio-2 .line-effect {
    gap: 1px;
  }
}
.portfolio-2 .line:nth-child(1) {
  height: 1px;
}
.portfolio-2 .line:nth-child(2) {
  height: 5px;
}
.portfolio-2 .line:nth-child(3) {
  height: 10px;
}
.portfolio-2 .line:nth-child(4) {
  height: 20px;
}
.portfolio-2 .line:nth-child(5) {
  height: 30px;
}
.portfolio-2 .line:nth-child(6) {
  height: 40px;
}
.portfolio-2 .line:nth-child(7) {
  height: 50px;
}
.portfolio-2 .line:nth-child(8) {
  height: 60px;
}
.portfolio-2 .line:nth-child(9) {
  height: 70px;
}
.portfolio-2 .line:nth-child(10) {
  height: 80px;
}
.portfolio-2 .line:nth-child(11) {
  height: 90px;
}
.portfolio-2 .line:nth-child(12) {
  height: 100px;
}
.portfolio-2 .line:nth-child(13) {
  height: 100px;
}
.portfolio-2 .line:nth-child(14) {
  height: 100px;
}
.portfolio-2 .line:nth-child(15) {
  height: 100px;
}
.portfolio-2 .line:nth-child(16) {
  height: 100px;
}
.portfolio-2 .line:nth-child(17) {
  height: 100px;
}
.portfolio-2 .line:nth-child(18) {
  height: 100px;
}
.portfolio-2 .line:nth-child(19) {
  height: 100px;
}
.portfolio-2 .line:nth-child(20) {
  height: 100px;
}
.portfolio-2 .line {
  width: 100%;
  background: #fff;
  transition: transform 0.8s ease-in-out, height 0.5s ease-in-out;
  transform-origin: center;
}
.portfolio-2 .swiper-slide-active .line {
  transform: scaleY(0);
}
.portfolio-2 .swiper-slide-active .line-effect {
  transform: scaleY(-50px);
}
.portfolio-2__item {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: 0.8s cubic-bezier(0.37, 0.23, 0, 0.96);
}
.portfolio-2__content {
  z-index: 9;
  left: 50px;
  bottom: 200px;
  position: absolute;
}
@media (max-width: 575px) {
  .portfolio-2__content {
    top: 120px;
    left: 30px;
  }
}
.portfolio-2__content-title {
  opacity: 0;
  font-size: 100px;
  line-height: 1.05;
  color: var(--white);
  transform: translateY(-130px);
}
.portfolio-2__content-title a:hover {
  color: var(--white);
}
@media (max-width: 575px) {
  .portfolio-2__content-title {
    font-size: 60px;
  }
}
.portfolio-2__list {
  opacity: 0;
  display: flex;
  margin-top: 40px;
  align-items: center;
  transform: translateY(-150px);
}
.portfolio-2__list li a {
  font-size: 14px;
  border-radius: 20px;
  padding: 10px 17px;
  border-radius: 20px;
  color: var(--white);
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.portfolio-2 .swiper-slide-active .portfolio-2__item {
  animation-name: qodef-animate-slide-out;
  animation-duration: 1.3s;
  animation-fill-mode: forwards;
}
.portfolio-2 .swiper-slide-active .portfolio-2__content-title {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2200ms ease;
}
.portfolio-2 .swiper-slide-active .portfolio-2__list {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2000ms ease;
}
.portfolio-2__slider__arrow {
  gap: 40px;
  right: 50px;
  bottom: 50px;
  z-index: 99;
  display: flex;
  position: absolute;
  align-items: center;
}
@media only screen and (max-width: 991px) {
  .portfolio-2__slider__arrow {
    gap: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-2__slider__arrow {
    left: 50px;
  }
}
@media (max-width: 575px) {
  .portfolio-2__slider__arrow {
    left: 30px;
  }
}
.portfolio-2__slider__arrow-prev, .portfolio-2__slider__arrow-next {
  gap: 8px;
  display: flex;
  font-size: 14px;
  font-weight: 600;
  align-items: center;
  color: var(--white);
}
.portfolio-2 .portfolio-2-pagination {
  position: absolute;
  bottom: 50px;
  left: 50px;
  z-index: 9;
  display: inline-block;
}
@media (max-width: 575px) {
  .portfolio-2 .portfolio-2-pagination {
    left: 30px;
  }
}
.portfolio-2 .swiper-pagination-bullet {
  width: 150px;
  height: 10px;
  display: inline-block;
  margin: 0 5px;
  overflow: hidden;
  transition: 0.4s;
  position: relative;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
@media only screen and (max-width: 1199px) {
  .portfolio-2 .swiper-pagination-bullet {
    width: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .portfolio-2 .swiper-pagination-bullet {
    width: 90px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-2 .swiper-pagination-bullet {
    width: 70px;
    bottom: 45px;
  }
}
.portfolio-2 .swiper-pagination-bullet::before {
  content: "";
  width: 150px;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 9;
  left: 0;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
.portfolio-2 .swiper-pagination-bullet::after {
  content: "";
  width: 0;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 8;
  left: 0;
  background-color: white;
}
.portfolio-2 .swiper-pagination-bullet-active::after {
  opacity: 1;
  width: 100%;
}

.portfolio-3__item {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: 0.8s cubic-bezier(0.37, 0.23, 0, 0.96);
}
.portfolio-3 .grid-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(4, 1fr);
}
.portfolio-3 .grid-mask div {
  background-color: var(--white);
  width: 100%;
  height: 100%;
  transition: opacity 0.6s ease-in-out;
}
.portfolio-3 .swiper-slide-active .grid-mask div {
  opacity: 0;
}
.portfolio-3__content {
  z-index: 9;
  left: 50px;
  bottom: 200px;
  position: absolute;
}
@media (max-width: 575px) {
  .portfolio-3__content {
    top: 120px;
    left: 30px;
  }
}
.portfolio-3__content-title {
  opacity: 0;
  font-size: 100px;
  line-height: 1.05;
  color: var(--white);
  transform: translateY(-130px);
}
.portfolio-3__content-title a:hover {
  color: var(--white);
}
@media (max-width: 575px) {
  .portfolio-3__content-title {
    font-size: 60px;
  }
}
.portfolio-3__list {
  opacity: 0;
  display: flex;
  margin-top: 40px;
  align-items: center;
  transform: translateY(-150px);
}
.portfolio-3__list li a {
  font-size: 14px;
  border-radius: 20px;
  padding: 10px 17px;
  border-radius: 20px;
  color: var(--white);
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.portfolio-3 .swiper-slide-active .portfolio-3__item {
  animation-name: qodef-animate-slide-out;
  animation-duration: 1.3s;
  animation-fill-mode: forwards;
}
.portfolio-3 .swiper-slide-active .portfolio-3__content-title {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2200ms ease;
}
.portfolio-3 .swiper-slide-active .portfolio-3__list {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2000ms ease;
}
.portfolio-3__slider__arrow {
  gap: 40px;
  right: 50px;
  bottom: 50px;
  z-index: 99;
  display: flex;
  position: absolute;
  align-items: center;
}
@media only screen and (max-width: 991px) {
  .portfolio-3__slider__arrow {
    gap: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-3__slider__arrow {
    left: 50px;
  }
}
@media (max-width: 575px) {
  .portfolio-3__slider__arrow {
    left: 30px;
  }
}
.portfolio-3__slider__arrow-prev, .portfolio-3__slider__arrow-next {
  gap: 8px;
  display: flex;
  font-size: 14px;
  font-weight: 600;
  align-items: center;
  color: var(--white);
}
.portfolio-3 .portfolio-3-pagination {
  position: absolute;
  bottom: 50px;
  left: 50px;
  z-index: 9;
  display: inline-block;
}
@media (max-width: 575px) {
  .portfolio-3 .portfolio-3-pagination {
    left: 30px;
  }
}
.portfolio-3 .swiper-pagination-bullet {
  width: 150px;
  height: 10px;
  display: inline-block;
  margin: 0 5px;
  overflow: hidden;
  transition: 0.4s;
  position: relative;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
@media only screen and (max-width: 1199px) {
  .portfolio-3 .swiper-pagination-bullet {
    width: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .portfolio-3 .swiper-pagination-bullet {
    width: 90px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-3 .swiper-pagination-bullet {
    width: 70px;
    bottom: 45px;
  }
}
.portfolio-3 .swiper-pagination-bullet::before {
  content: "";
  width: 150px;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 9;
  left: 0;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
.portfolio-3 .swiper-pagination-bullet::after {
  content: "";
  width: 0;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 8;
  left: 0;
  background-color: white;
}
.portfolio-3 .swiper-pagination-bullet-active::after {
  opacity: 1;
  width: 100%;
}

.portfolio-4 .slider {
  min-height: 50vh;
}
.portfolio-4 .slider .swiper-slide {
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
}
.portfolio-4 .slider .swiper-slide .slide-inner {
  position: absolute;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
}
.portfolio-4 .slider .swiper-slide .slide-inner img {
  position: absolute;
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.portfolio-4 .swiper-container {
  width: 100%;
  height: 100vh;
  position: relative;
}
.portfolio-4__content {
  z-index: 9;
  left: 50px;
  bottom: 200px;
  position: absolute;
}
@media (max-width: 575px) {
  .portfolio-4__content {
    top: 120px;
    left: 30px;
  }
}
.portfolio-4__content-title {
  opacity: 0;
  font-size: 100px;
  line-height: 1.05;
  color: var(--white);
  transform: translateY(-130px);
}
.portfolio-4__content-title a:hover {
  color: var(--white);
}
@media (max-width: 575px) {
  .portfolio-4__content-title {
    font-size: 60px;
  }
}
.portfolio-4__list {
  opacity: 0;
  display: flex;
  margin-top: 40px;
  align-items: center;
  transform: translateY(-150px);
}
.portfolio-4__list li a {
  font-size: 14px;
  border-radius: 20px;
  padding: 10px 17px;
  border-radius: 20px;
  color: var(--white);
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.portfolio-4 .swiper-slide-active .portfolio-4__item {
  animation-name: qodef-animate-slide-out;
  animation-duration: 1.3s;
  animation-fill-mode: forwards;
}
.portfolio-4 .swiper-slide-active .portfolio-4__content-title {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2200ms ease;
}
.portfolio-4 .swiper-slide-active .portfolio-4__list {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2000ms ease;
}
.portfolio-4__slider {
  width: 100vw;
  height: 100vh;
}
.portfolio-4__slider__arrow {
  gap: 40px;
  right: 50px;
  bottom: 50px;
  z-index: 99;
  display: flex;
  position: absolute;
  align-items: center;
}
@media only screen and (max-width: 991px) {
  .portfolio-4__slider__arrow {
    gap: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-4__slider__arrow {
    left: 50px;
  }
}
@media (max-width: 575px) {
  .portfolio-4__slider__arrow {
    left: 30px;
  }
}
.portfolio-4__slider__arrow-prev, .portfolio-4__slider__arrow-next {
  gap: 8px;
  display: flex;
  font-size: 14px;
  font-weight: 600;
  align-items: center;
  color: var(--white);
}
.portfolio-4 .portfolio-4-pagination {
  position: absolute;
  bottom: 50px;
  left: 50px;
  z-index: 9;
  display: flex;
  gap: 15px;
  top: inherit;
  right: 0;
  transform: inherit;
}
@media (max-width: 575px) {
  .portfolio-4 .portfolio-4-pagination {
    left: 30px;
  }
}
.portfolio-4 .swiper-pagination-bullet {
  width: 150px;
  height: 10px;
  display: inline-block;
  margin: 0 5px;
  overflow: hidden;
  transition: 0.4s;
  position: relative;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
@media only screen and (max-width: 1199px) {
  .portfolio-4 .swiper-pagination-bullet {
    width: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .portfolio-4 .swiper-pagination-bullet {
    width: 90px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-4 .swiper-pagination-bullet {
    width: 70px;
    bottom: 45px;
  }
}
.portfolio-4 .swiper-pagination-bullet::before {
  content: "";
  width: 150px;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 9;
  left: 0;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
.portfolio-4 .swiper-pagination-bullet::after {
  content: "";
  width: 0;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 8;
  left: 0;
  background-color: white;
}
.portfolio-4 .swiper-pagination-bullet-active::after {
  opacity: 1;
  width: 100%;
}

.portfolio-5 {
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-width: 100vw;
}
.portfolio-5__item {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
}
.portfolio-5__content {
  z-index: 9;
  left: 50px;
  bottom: 200px;
  position: absolute;
}
@media (max-width: 575px) {
  .portfolio-5__content {
    top: 120px;
    left: 30px;
  }
}
.portfolio-5__content-title {
  opacity: 0;
  font-size: 100px;
  line-height: 1.05;
  color: var(--white);
  transform: translateY(-130px);
}
.portfolio-5__content-title a:hover {
  color: var(--white);
}
@media (max-width: 575px) {
  .portfolio-5__content-title {
    font-size: 60px;
  }
}
.portfolio-5__list {
  opacity: 0;
  display: flex;
  margin-top: 40px;
  align-items: center;
  transform: translateY(-150px);
}
.portfolio-5__list li a {
  font-size: 14px;
  border-radius: 20px;
  padding: 10px 17px;
  border-radius: 20px;
  color: var(--white);
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.portfolio-5 .swiper-slide-active .portfolio-5__item {
  animation-name: qodef-animate-slide-out;
  animation-duration: 1.3s;
  animation-fill-mode: forwards;
}
.portfolio-5 .swiper-slide-active .portfolio-5__content-title {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2200ms ease;
}
.portfolio-5 .swiper-slide-active .portfolio-5__list {
  opacity: 1;
  transform: translatey(0px);
  transition: all 2000ms ease;
}
.portfolio-5__slider {
  width: 100vw;
  height: 100vh;
}
.portfolio-5__slider__arrow {
  gap: 40px;
  right: 50px;
  bottom: 50px;
  z-index: 99;
  display: flex;
  position: absolute;
  align-items: center;
}
@media only screen and (max-width: 991px) {
  .portfolio-5__slider__arrow {
    gap: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-5__slider__arrow {
    left: 50px;
  }
}
@media (max-width: 575px) {
  .portfolio-5__slider__arrow {
    left: 30px;
  }
}
.portfolio-5__slider__arrow-prev, .portfolio-5__slider__arrow-next {
  gap: 8px;
  display: flex;
  font-size: 14px;
  font-weight: 600;
  align-items: center;
  color: var(--white);
}
.portfolio-5 .swiper {
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-width: 100vw;
}
.portfolio-5 img {
  margin: 0 !important;
  padding: 0 !important;
}
.portfolio-5 .swiper-slicer-image {
  max-width: unset;
}
.portfolio-5 .portfolio-5-pagination {
  position: absolute;
  bottom: 50px;
  left: 50px;
  z-index: 9;
  display: flex;
  gap: 15px;
  top: inherit;
  right: 0;
  transform: inherit;
}
@media (max-width: 575px) {
  .portfolio-5 .portfolio-5-pagination {
    left: 30px;
  }
}
.portfolio-5 .swiper-pagination-bullet {
  width: 150px;
  height: 10px;
  display: inline-block;
  margin: 0 5px;
  overflow: hidden;
  transition: 0.4s;
  position: relative;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
@media only screen and (max-width: 1199px) {
  .portfolio-5 .swiper-pagination-bullet {
    width: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .portfolio-5 .swiper-pagination-bullet {
    width: 90px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-5 .swiper-pagination-bullet {
    width: 70px;
    bottom: 45px;
  }
}
.portfolio-5 .swiper-pagination-bullet::before {
  content: "";
  width: 150px;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 9;
  left: 0;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
}
.portfolio-5 .swiper-pagination-bullet::after {
  content: "";
  width: 0;
  height: 100%;
  position: absolute;
  transition: 0.6s;
  z-index: 8;
  left: 0;
  background-color: white;
}
.portfolio-5 .swiper-pagination-bullet-active::after {
  opacity: 1;
  width: 100%;
}

/* parallax carousal page css */
.body-parallax-carosole .header-area-8 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  right: 0;
}

.parallax-slider-wrapper {
  width: 100%;
  height: 100vh;
  display: flex;
  overflow: hidden;
  margin-left: 10px;
  position: relative;
  align-items: center;
  justify-content: flex-start;
}

.parallax-slider-inner {
  gap: 10px;
  width: 100%;
  height: 100vh;
  display: flex;
  padding-top: 80px;
  align-items: center;
  justify-content: flex-start;
}

.parallax-slider-item {
  width: 500px;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-size: cover;
}
.parallax-slider-item img {
  height: 80%;
  min-width: 750px;
  object-fit: cover;
  margin-left: -50px;
  background-size: cover;
  background-position: center;
  cursor: none;
}
.parallax-slider-item .content {
  margin-top: 24px;
}
@media only screen and (max-width: 1199px) {
  .parallax-slider-item .content {
    margin-top: 14px;
  }
}
.parallax-slider-item .title {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  letter-spacing: -0.05em;
}
@media only screen and (max-width: 1199px) {
  .parallax-slider-item .title {
    font-size: 18px;
  }
}
.parallax-slider-item .tag {
  display: block;
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  color: var(--primary);
  letter-spacing: -0.05em;
  font-family: var(--font_bdogrotesk);
}
@media only screen and (max-width: 1199px) {
  .parallax-slider-item .tag {
    font-size: 18px;
  }
}
.parallax-slider-item .date {
  display: block;
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  color: var(--primary);
  letter-spacing: -0.05em;
  font-family: var(--font_bdogrotesk);
}
@media only screen and (max-width: 1199px) {
  .parallax-slider-item .date {
    font-size: 18px;
  }
}

/* portfolio showcase page css */
@media (min-width: 1650px) {
  .body-portfolio-showcase .container.large {
    max-width: 1650px;
  }
}
.body-portfolio-showcase .header-area-2 .side-toggle {
  background-color: #F3F3F3;
}
.body-portfolio-showcase .footer-area-4 .footer-widget-wrapper-box {
  margin-top: 0px;
}

/* work area 5 style  */
.work-area-5 .works-wrapper-box {
  border-top: 1px solid var(--border);
  margin-top: 80px;
  padding-top: 70px;
}
@media only screen and (max-width: 1919px) {
  .work-area-5 .works-wrapper-box {
    padding-top: 50px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-5 .works-wrapper-box {
    padding-top: 40px;
  }
}

.works-wrapper-5 {
  display: grid;
  gap: 40px 40px;
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-5 {
    gap: 30px 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-5 {
    gap: 20px 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-5 {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-5 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .works-wrapper-5 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.works-wrapper-5 > *.span-2 {
  grid-column: span 2;
}
.works-wrapper-5 > *.grid-column-start-1 {
  grid-column-start: 1;
}
@media only screen and (max-width: 991px) {
  .works-wrapper-5 > *.grid-column-start-1 {
    grid-column-start: auto;
  }
}
.works-wrapper-5 > *.grid-column-start-2 {
  grid-column-start: 2;
}
@media only screen and (max-width: 991px) {
  .works-wrapper-5 > *.grid-column-start-2 {
    grid-column-start: auto;
  }
}
.works-wrapper-5 > *.grid-column-start-3 {
  grid-column-start: 3;
}
@media only screen and (max-width: 991px) {
  .works-wrapper-5 > *.grid-column-start-3 {
    grid-column-start: auto;
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-5 > *:nth-child(n) {
    grid-column-start: auto;
  }
}
.works-wrapper-5 .work-box {
  min-height: 375px;
}
.works-wrapper-5 .work-box .thumb {
  border-radius: 15px;
  overflow: hidden;
}
.works-wrapper-5 .work-box .thumb img {
  width: 100%;
}
.works-wrapper-5 .section-header {
  margin-top: -8px;
}
.works-wrapper-5 .section-header .section-title {
  font-size: 36px;
  font-weight: 400;
  line-height: 1.33;
  letter-spacing: -0.05em;
  max-width: 440px;
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-5 .section-header .section-title {
    font-size: 30px;
    max-width: 370px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-5 .section-header .section-title {
    font-size: 24px;
    max-width: 300px;
  }
}
.works-wrapper-5 .section-header .header-shape-1 {
  margin-top: 44px;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-5 .section-header .header-shape-1 {
    margin-top: 24px;
  }
}
.works-wrapper-5 .section-header .header-shape-1 img {
  width: 65px;
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-5 .section-header .header-shape-1 img {
    width: 55px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-5 .section-header .header-shape-1 img {
    width: 45px;
  }
}
.works-wrapper-5 .services-wrapper-box {
  margin-top: -7px;
  margin-bottom: -6px;
  display: flex;
  gap: 20px;
  flex-direction: column;
  justify-content: space-between;
}
.works-wrapper-5 .services-wrapper-box .subtitle {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}
.works-wrapper-5 .service-box .title {
  font-size: 36px;
  font-weight: 400;
  line-height: 1.33;
  letter-spacing: -0.05em;
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-5 .service-box .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-5 .service-box .title {
    font-size: 24px;
  }
}
.works-wrapper-5 .service-box .title a:hover {
  color: var(--secondary);
}

.card-wrap {
  transform: perspective(700px);
  transform-style: preserve-3d;
  cursor: pointer;
  max-height: 375px;
  position: relative;
}
.card-wrap:hover .card-bg {
  transition: 0.6s cubic-bezier(0.23, 1, 0.32, 1), opacity 1s cubic-bezier(0.23, 1, 0.32, 1);
  opacity: 1;
}
.card-wrap:hover .card {
  transition: 0.6s cubic-bezier(0.23, 1, 0.32, 1), cubic-bezier(0.23, 1, 0.32, 1);
}

.card {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-repeat: no-repeat;
  overflow: hidden;
  border-radius: 10px;
  transition: 1s cubic-bezier(0.445, 0.05, 0.55, 0.95);
  will-change: transform;
  border: none;
}

.card-bg {
  position: absolute;
  top: -18px;
  left: -18px;
  width: 110%;
  height: 110%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  transition: 1s cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity 5s 1s cubic-bezier(0.445, 0.05, 0.55, 0.95);
  pointer-events: none;
}

/* portfolio showcase 2 page css */
@media (min-width: 1850px) {
  .body-modern-agency .container.large {
    max-width: 1850px;
  }
}
.body-modern-agency .rr-btn {
  padding: 15px 27px;
}
.body-modern-agency .section-subtitle {
  font-family: var(--font_tartuffotrial);
  font-weight: 100;
  font-size: 20px;
  line-height: 26px;
  text-transform: none;
}
.body-modern-agency .footer-area-3 .footer-widget-wrapper-box {
  border-top: 1px solid var(--border);
}

/* hero area 7 style  */
.hero-area-7 .section-title {
  font-weight: 100;
  font-size: 140px;
  line-height: 0.93;
  text-align: center;
  max-width: 1115px;
  margin-inline: auto;
}
@media only screen and (max-width: 1919px) {
  .hero-area-7 .section-title {
    font-size: 110px;
    max-width: 915px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-7 .section-title {
    font-size: 90px;
    max-width: 715px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-7 .section-title {
    font-size: 70px;
    max-width: 615px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-7 .section-title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-7 .section-title {
    font-size: 45px;
  }
}
@media (max-width: 575px) {
  .hero-area-7 .section-title {
    font-size: 40px;
  }
}
.hero-area-7 .section-title span {
  color: rgba(17, 17, 17, 0.3);
}
.dark .hero-area-7 .section-title span {
  color: rgba(255, 255, 255, 0.3);
}
.hero-area-7 .section-content {
  text-align: center;
}
.hero-area-7 .section-content .text {
  font-family: var(--font_tartuffotrial);
  font-weight: 100;
  font-size: 36px;
  line-height: 1.11;
  color: var(--primary);
  max-width: 620px;
  text-align: center;
  margin-inline: auto;
}
@media only screen and (max-width: 1919px) {
  .hero-area-7 .section-content .text {
    font-size: 30px;
    max-width: 520px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-7 .section-content .text {
    font-size: 24px;
    max-width: 420px;
  }
}
@media only screen and (max-width: 767px) {
  .hero-area-7 .section-content .text {
    font-size: 20px;
    max-width: 420px;
  }
}
.hero-area-7 .section-content .text-wrapper {
  margin-top: 45px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-7 .section-content .text-wrapper {
    margin-top: 35px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-7 .section-content .text-wrapper {
    margin-top: 25px;
  }
}
.hero-area-7 .section-content .btn-wrapper {
  margin-top: 33px;
}
@media only screen and (max-width: 1199px) {
  .hero-area-7 .section-content .btn-wrapper {
    margin-top: 23px;
  }
}
.hero-area-7 .section-title-wrapper {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  margin-top: 80px;
  padding-top: 61px;
  padding-bottom: 55px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-7 .section-title-wrapper {
    padding-top: 51px;
    padding-bottom: 45px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-7 .section-title-wrapper {
    padding-top: 31px;
    padding-bottom: 25px;
  }
}

/* work area 7 style  */
.work-area-7 .works-wrapper-box {
  margin-top: 50px;
}
@media only screen and (max-width: 1919px) {
  .work-area-7 .works-wrapper-box {
    margin-top: 40px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-7 .works-wrapper-box {
    margin-top: 30px;
  }
}

.works-wrapper-7 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  border-left: 1px solid var(--border);
  border-right: 1px solid var(--border);
}
@media (max-width: 575px) {
  .works-wrapper-7 {
    grid-template-columns: 1fr;
  }
}
.works-wrapper-7 > * {
  border-top: 1px solid var(--border);
  padding: 150px;
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-7 > * {
    padding: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-7 > * {
    padding: 40px;
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-7 > * {
    padding: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-7 > * {
    padding: 10px;
  }
}
.works-wrapper-7 > *:nth-child(1) {
  border-top: 0;
}
.works-wrapper-7 > *:nth-child(2) {
  border-top: 0;
}
@media (max-width: 575px) {
  .works-wrapper-7 > *:nth-child(2) {
    border-top: 1px solid var(--border);
  }
}
.works-wrapper-7 > *:nth-child(2n+1) {
  border-right: 1px solid var(--border);
}
@media (max-width: 575px) {
  .works-wrapper-7 > *:nth-child(2n+1) {
    border-right: 0;
  }
}
.works-wrapper-7 .work-box .thumb {
  position: relative;
}
.works-wrapper-7 .work-box .thumb .image {
  overflow: hidden;
  position: relative;
  transform: scale(0.9);
}
.works-wrapper-7 .work-box .thumb .image img {
  transform-origin: center;
}
.works-wrapper-7 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-7 .work-box .content {
  position: absolute;
  bottom: 20px;
  left: 20px;
  visibility: hidden;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-7 .work-box .content {
    visibility: visible;
  }
}
.works-wrapper-7 .work-box .title {
  font-weight: 300;
  font-size: 30px;
  line-height: 0.9;
  background-color: var(--white);
  padding: 15px 20px 13px;
  color: var(--black);
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-7 .work-box .title {
    font-size: 26px;
    padding: 10px 15px 8px;
  }
}
.works-wrapper-7 .work-box .meta {
  font-family: var(--font_tartuffotrial);
  font-weight: 300;
  font-style: italic;
  font-size: 16px;
  line-height: 1.69;
  background-color: var(--white);
  display: inline-block;
  padding: 5px 15px 3px;
  margin-top: 3px;
  color: var(--black);
}
.works-wrapper-7 .btn-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 50px 0;
}
.works-wrapper-7 .btn-wrapper a {
  font-family: var(--font_tartuffotrial);
  font-weight: 100;
  font-size: 36px;
  line-height: 1.1;
  text-align: center;
  color: var(--primary);
  max-width: 225px;
  text-decoration: none;
  display: inline-block;
}
@media only screen and (max-width: 1399px) {
  .works-wrapper-7 .btn-wrapper a {
    font-size: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-7 .btn-wrapper a {
    font-size: 26px;
    max-width: 165px;
  }
}
@media only screen and (max-width: 991px) {
  .works-wrapper-7 .btn-wrapper a {
    font-size: 20px;
    max-width: 135px;
  }
}
.works-wrapper-7 .btn-wrapper a:hover .underline {
  background-size: 0% 100%;
}
.works-wrapper-7 .btn-wrapper .underline {
  width: 100%;
  background-image: linear-gradient(transparent calc(100% - 1px), var(--primary) 1px);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transition: background-size 1s;
  background-position: 0 -6px;
}
@media only screen and (max-width: 1399px) {
  .works-wrapper-7 .btn-wrapper .underline {
    background-position: 0 -2px;
  }
}

/* capabilities area 2 style  */
.capabilities-area-2 .section-content-wrapper {
  margin-top: 14px;
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 1fr 1235px;
  margin-bottom: 31px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-2 .section-content-wrapper {
    grid-template-columns: 1fr 950px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area-2 .section-content-wrapper {
    grid-template-columns: 1fr 800px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area-2 .section-content-wrapper {
    grid-template-columns: 1fr 600px;
  }
}
@media only screen and (max-width: 991px) {
  .capabilities-area-2 .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.capabilities-area-2 .section-content {
  margin-top: 27px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-2 .section-content {
    margin-top: 17px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area-2 .section-content {
    margin-top: 7px;
  }
}
.capabilities-area-2 .section-content .section-title-wrapper {
  display: block;
}
@media only screen and (max-width: 767px) {
  .capabilities-area-2 .capability-wrapper {
    border-top: 1px solid var(--border);
  }
}
@media only screen and (max-width: 767px) {
  .capabilities-area-2 .capability-box {
    border-bottom: 1px solid var(--border);
    padding-bottom: 20px;
    padding-top: 20px;
  }
}
.capabilities-area-2 .capability-box-inner {
  display: grid;
  gap: 10px 60px;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 575px) {
  .capabilities-area-2 .capability-box-inner {
    grid-template-columns: 1fr;
  }
}
.capabilities-area-2 .capability-box:hover .thumb img, .capabilities-area-2 .capability-box.active .thumb img {
  opacity: 1;
  transform: scale(1);
}
.capabilities-area-2 .capability-box .title {
  font-weight: 100;
  font-size: 100px;
  line-height: 1.1;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-2 .capability-box .title {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area-2 .capability-box .title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area-2 .capability-box .title {
    font-size: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .capabilities-area-2 .capability-box .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .capabilities-area-2 .capability-box .title {
    font-size: 35px;
  }
}
.capabilities-area-2 .capability-box .title.rr-btn-underline {
  padding-bottom: 0;
  color: rgba(17, 17, 17, 0.3);
  text-transform: unset;
}
.dark .capabilities-area-2 .capability-box .title.rr-btn-underline {
  color: rgba(255, 255, 255, 0.3);
}
.capabilities-area-2 .capability-box .title.rr-btn-underline::before {
  height: 3px;
  transition: 0.5s;
  bottom: 9px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-2 .capability-box .title.rr-btn-underline::before {
    bottom: 6px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area-2 .capability-box .title.rr-btn-underline::before {
    height: 2px;
    bottom: 3px;
  }
}
.capabilities-area-2 .capability-box .thumb {
  display: flex;
  gap: 10px;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-2 .capability-box .thumb {
    gap: 10px;
  }
}
.capabilities-area-2 .capability-box .thumb img {
  width: 76px;
  height: 76px;
  border-radius: 15px;
  object-fit: cover;
  opacity: 0;
  transform: scale(0);
  transform-origin: top right;
  transition: all 0.5s;
}
@media only screen and (max-width: 1919px) {
  .capabilities-area-2 .capability-box .thumb img {
    width: 64px;
    height: 64px;
    border-radius: 10px;
  }
}
@media only screen and (max-width: 1399px) {
  .capabilities-area-2 .capability-box .thumb img {
    width: 50px;
    height: 50px;
  }
}
@media only screen and (max-width: 1199px) {
  .capabilities-area-2 .capability-box .thumb img {
    width: 40px;
    height: 40px;
    opacity: 1;
    transform: scale(1);
  }
}

/* award area 3 style  */
.award-area-3 .section-header {
  margin-top: 19px;
}
.award-area-3 .section-title {
  max-width: 855px;
  text-indent: 2.3em;
}
@media only screen and (max-width: 1919px) {
  .award-area-3 .section-title {
    max-width: 755px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-3 .section-title {
    max-width: 555px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-3 .section-title {
    max-width: 505px;
  }
}
@media only screen and (max-width: 991px) {
  .award-area-3 .section-title {
    max-width: 635px;
  }
}
.award-area-3 .section-title span {
  position: relative;
  padding: 0 32px;
}
@media only screen and (max-width: 1919px) {
  .award-area-3 .section-title span {
    padding: 0 22px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-3 .section-title span {
    padding: 0 17px;
  }
}
@media (max-width: 575px) {
  .award-area-3 .section-title span {
    padding: 0;
  }
}
.award-area-3 .section-title span:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 72%;
  border: 2px solid var(--primary);
  border-radius: 100px;
  top: 53%;
  left: 0;
  transform: translate(0, -50%);
  z-index: -1;
}
@media only screen and (max-width: 1399px) {
  .award-area-3 .section-title span:before {
    border-width: 1px;
  }
}
@media (max-width: 575px) {
  .award-area-3 .section-title span:before {
    display: none;
  }
}
.award-area-3 .award-wrapper-box {
  max-width: 1235px;
  margin-left: auto;
  margin-top: 85px;
}
@media only screen and (max-width: 1919px) {
  .award-area-3 .award-wrapper-box {
    max-width: 1000px;
    margin-top: 55px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-3 .award-wrapper-box {
    max-width: 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-3 .award-wrapper-box {
    max-width: 750px;
    margin-top: 45px;
  }
}
.award-area-3 .award-wrapper {
  border-top: 1px solid var(--border);
}
.award-area-3 .award-box {
  border-bottom: 1px solid var(--border);
  padding-top: 40px;
  padding-bottom: 40px;
  display: grid;
  gap: 20px 50px;
  grid-template-columns: 280px 1fr 100px;
  align-items: center;
  transition: all 0.5s;
}
@media only screen and (max-width: 1919px) {
  .award-area-3 .award-box {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-3 .award-box {
    grid-template-columns: 180px 1fr 100px;
  }
}
@media only screen and (max-width: 767px) {
  .award-area-3 .award-box {
    grid-template-columns: 1fr 1fr;
  }
}
.award-area-3 .award-box:hover {
  background-color: #F9F9F9;
}
.dark .award-area-3 .award-box:hover {
  background-color: #171717;
}
@media only screen and (max-width: 767px) {
  .award-area-3 .award-box:hover {
    background-color: transparent;
  }
}
.award-area-3 .award-box:hover .category {
  transform: translateX(30px);
}
@media only screen and (max-width: 767px) {
  .award-area-3 .award-box:hover .category {
    transform: translateX(0px);
  }
}
.award-area-3 .award-box:hover .year {
  transform: translateX(-30px);
}
@media only screen and (max-width: 767px) {
  .award-area-3 .award-box:hover .year {
    transform: translateX(0px);
  }
}
.award-area-3 .award-box .category {
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  display: inline-block;
  color: var(--primary);
  transition: all 0.5s;
}
.award-area-3 .award-box .award {
  font-size: 24px;
  font-weight: 400;
  line-height: 18px;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .award-area-3 .award-box .award {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .award-area-3 .award-box .award {
    order: 3;
    grid-column: span 2;
  }
}
.award-area-3 .award-box .year {
  font-size: 18px;
  font-weight: 400;
  line-height: 18px;
  display: inline-block;
  color: var(--primary);
  transition: all 0.5s;
  text-align: right;
}

/* cta area 5 style  */
.cta-area-5-inner {
  overflow: hidden;
}
.cta-area-5 .section-title {
  font-size: 200px;
  font-weight: 100;
  line-height: 0.85;
  text-transform: uppercase;
  white-space: nowrap;
}
@media only screen and (max-width: 1919px) {
  .cta-area-5 .section-title {
    font-size: 140px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-5 .section-title {
    font-size: 100px;
  }
}
@media only screen and (max-width: 1199px) {
  .cta-area-5 .section-title {
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .cta-area-5 .section-title {
    font-size: 60px;
  }
}
@media (max-width: 575px) {
  .cta-area-5 .section-title {
    font-size: 40px;
  }
}
.cta-area-5 .section-title a {
  display: inline-flex;
  align-items: center;
}
.cta-area-5 .section-title .line {
  width: 0.7em;
  height: 0.05em;
  background-color: var(--primary);
  display: inline-block;
  align-self: center;
  margin-left: 0.3em;
  margin-right: 0.2em;
}
.cta-area-5 .section-header {
  margin-top: 45px;
  margin-bottom: 87px;
}
@media only screen and (max-width: 1919px) {
  .cta-area-5 .section-header {
    margin-top: 25px;
    margin-bottom: 67px;
  }
}
@media only screen and (max-width: 1399px) {
  .cta-area-5 .section-header {
    margin-top: 5px;
    margin-bottom: 47px;
  }
}
.cta-area-5 .section-header .title-wrapper {
  animation: 45s t-slide infinite linear;
}
.cta-area-5 .section-header .t-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  padding: 10px 20px;
  display: inline-block;
  background-color: var(--theme);
  color: var(--black);
  border-radius: 50px;
  position: absolute;
  top: 0;
  left: 0;
  margin: -25px 0 0 -65px;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 404 page css */
.error-area-inner {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.error-area .section-subtitle {
  font-family: var(--font_sequelsansmediumbody);
  font-size: 300px;
  font-weight: 315;
  line-height: 0.7;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 1919px) {
  .error-area .section-subtitle {
    font-size: 240px;
  }
}
@media only screen and (max-width: 1399px) {
  .error-area .section-subtitle {
    font-size: 180px;
  }
}
@media only screen and (max-width: 1199px) {
  .error-area .section-subtitle {
    font-size: 140px;
  }
}
@media only screen and (max-width: 991px) {
  .error-area .section-subtitle {
    font-size: 120px;
  }
}
.error-area .section-title {
  max-width: 740px;
}
@media only screen and (max-width: 1919px) {
  .error-area .section-title {
    max-width: 640px;
  }
}
@media only screen and (max-width: 1399px) {
  .error-area .section-title {
    max-width: 540px;
  }
}
@media only screen and (max-width: 1199px) {
  .error-area .section-title {
    max-width: 440px;
  }
}
@media only screen and (max-width: 991px) {
  .error-area .section-title {
    max-width: 340px;
  }
}
.error-area .section-content {
  align-self: center;
}
.error-area .section-content .title-wrapper {
  margin-top: 37px;
}
@media only screen and (max-width: 1919px) {
  .error-area .section-content .title-wrapper {
    margin-top: 27px;
  }
}
@media only screen and (max-width: 1199px) {
  .error-area .section-content .title-wrapper {
    margin-top: 17px;
  }
}
.error-area .section-content .btn-wrapper {
  margin-top: 64px;
}
@media only screen and (max-width: 1919px) {
  .error-area .section-content .btn-wrapper {
    margin-top: 44px;
  }
}
@media only screen and (max-width: 1199px) {
  .error-area .section-content .btn-wrapper {
    margin-top: 34px;
  }
}
@media only screen and (max-width: 991px) {
  .error-area .section-content .btn-wrapper {
    margin-top: 24px;
  }
}

/* about page css */
.about-area-details {
  /* moving gallery style  */
}
.about-area-details .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .about-area-details .section-header {
    padding-top: 7px;
  }
}
.about-area-details .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-details .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-details .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-details .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.about-area-details .subtitle-wrapper {
  margin-top: 8px;
}
.about-area-details .section-title {
  max-width: 1030px;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .section-title {
    max-width: 840px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-details .section-title {
    max-width: 640px;
  }
}
.about-area-details .section-content .text {
  font-size: 30px;
  font-weight: 400;
  line-height: 1.26;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .section-content .text {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-details .section-content .text {
    font-size: 20px;
  }
}
.about-area-details .section-content .text:not(:first-child) {
  margin-top: 38px;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .section-content .text:not(:first-child) {
    margin-top: 28px;
  }
}
.about-area-details .section-content .btn-wrapper {
  margin-top: 61px;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .section-content .btn-wrapper {
    margin-top: 41px;
  }
}
.about-area-details .info-list li {
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  font-family: var(--font_sequelsansromanbody);
  position: relative;
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .info-list li {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-details .info-list li {
    font-size: 20px;
  }
}
.about-area-details .info-list li:not(:first-child) {
  margin-top: 4px;
}
.about-area-details .info-list li:before {
  content: "";
  width: 6px;
  height: 6px;
  background-color: var(--primary);
  margin-right: 10px;
}
@media only screen and (max-width: 1399px) {
  .about-area-details .info-list li:before {
    width: 4px;
    height: 4px;
  }
}
.about-area-details .section-content-wrapper {
  border-top: 1px solid var(--border);
  padding-top: 22px;
  margin-top: 64px;
  max-width: 1235px;
  margin-left: auto;
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 1fr 715px;
}
@media only screen and (max-width: 1919px) {
  .about-area-details .section-content-wrapper {
    margin-top: 54px;
    max-width: 1000px;
    grid-template-columns: 1fr 565px;
  }
}
@media only screen and (max-width: 1399px) {
  .about-area-details .section-content-wrapper {
    margin-top: 44px;
    max-width: 850px;
    grid-template-columns: 1fr 465px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area-details .section-content-wrapper {
    max-width: 750px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area-details .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.about-area-details .wrapper-gallery {
  display: inline-flex;
  align-items: flex-start;
  margin-top: 50px;
  padding: 0;
}
.about-area-details .moving-gallery li {
  width: 40vw;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
  list-style: none;
}
@media only screen and (max-width: 1199px) {
  .about-area-details .moving-gallery li {
    padding-left: 4px;
    padding-right: 4px;
  }
}
.about-area-details .moving-gallery li img {
  height: auto;
  margin: 0px;
  width: 100%;
}
.about-area-details .moving-gallery li:first-child {
  padding-left: 0px;
}
.about-area-details .moving-gallery li:last-child {
  padding-right: 0px;
}
.about-area-details .moving-gallery li:nth-child(1) {
  width: 30vw;
}
@media (max-width: 575px) {
  .about-area-details .moving-gallery li:nth-child(1) {
    width: 60vw;
  }
}
.about-area-details .moving-gallery li:nth-child(2) {
  width: 25vw;
}
@media (max-width: 575px) {
  .about-area-details .moving-gallery li:nth-child(2) {
    width: 50vw;
  }
}
.about-area-details .moving-gallery li:nth-child(3) {
  width: 20vw;
}
@media (max-width: 575px) {
  .about-area-details .moving-gallery li:nth-child(3) {
    width: 40vw;
  }
}
.about-area-details .moving-gallery li:nth-child(4) {
  width: 25vw;
}
@media (max-width: 575px) {
  .about-area-details .moving-gallery li:nth-child(4) {
    width: 50vw;
  }
}
.about-area-details .moving-gallery li:nth-child(5) {
  width: 30vw;
}
@media (max-width: 575px) {
  .about-area-details .moving-gallery li:nth-child(5) {
    width: 60vw;
  }
}

/* approach area about page style  */
.approach-area-about-page .section-title {
  max-width: 845px;
}
@media only screen and (max-width: 1919px) {
  .approach-area-about-page .section-title {
    max-width: 645px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-about-page .section-title {
    max-width: 545px;
  }
}
.approach-area-about-page .subtitle-wrapper {
  margin-top: 8px;
}
.approach-area-about-page .section-header {
  margin-top: 50px;
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .approach-area-about-page .section-header {
    margin-top: 10px;
    padding-top: 7px;
  }
}
.approach-area-about-page .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .approach-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.approach-area-about-page .approach-wrapper-box {
  max-width: 1235px;
  margin-left: auto;
  margin-top: 81px;
  margin-bottom: 43px;
}
@media only screen and (max-width: 1919px) {
  .approach-area-about-page .approach-wrapper-box {
    max-width: 1000px;
    margin-top: 61px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-about-page .approach-wrapper-box {
    max-width: 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area-about-page .approach-wrapper-box {
    max-width: 750px;
    margin-right: 0;
    margin-top: 41px;
  }
}
.approach-area-about-page .approach-wrapper {
  display: grid;
  gap: 40px 50px;
  grid-template-columns: 1fr 1fr auto;
}
@media only screen and (max-width: 767px) {
  .approach-area-about-page .approach-wrapper {
    grid-template-columns: 1fr;
  }
}
.approach-area-about-page .approach-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  display: flex;
  gap: 0 20px;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 1919px) {
  .approach-area-about-page .approach-box .title {
    font-size: 24px;
  }
}
.approach-area-about-page .approach-box .title img {
  width: 250px;
}
@media only screen and (max-width: 1919px) {
  .approach-area-about-page .approach-box .title img {
    width: 200px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-about-page .approach-box .title img {
    width: 150px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area-about-page .approach-box .title img {
    display: none;
  }
}
.approach-area-about-page .approach-box .approach-list {
  margin-top: 28px;
}
@media only screen and (max-width: 1199px) {
  .approach-area-about-page .approach-box .approach-list {
    margin-top: 18px;
  }
}
.approach-area-about-page .approach-box .approach-list li {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: var(--primary);
}

/* info area style  */
.info-area-page-about {
  background-color: var(--bg);
}
.info-area-page-about .section-header {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 50px;
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .info-area-page-about .section-header {
    margin-top: 10px;
    padding-top: 7px;
  }
}
.info-area-page-about .section-header .subtitle-wrapper {
  margin-top: 8px;
}
.info-area-page-about .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .info-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .info-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .info-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .info-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.info-area-page-about .section-subtitle {
  color: var(--white);
}
.info-area-page-about .section-title {
  color: var(--white);
  max-width: 805px;
}
.info-area-page-about .counter-wrapper-box {
  margin-top: 94px;
}
@media only screen and (max-width: 1919px) {
  .info-area-page-about .counter-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .info-area-page-about .counter-wrapper-box {
    margin-top: 44px;
  }
}
.info-area-page-about .counter-wrapper {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 991px) {
  .info-area-page-about .counter-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 767px) {
  .info-area-page-about .counter-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.info-area-page-about .funfact-item {
  padding: 39px 50px 41px;
  border-radius: 20px;
  background-color: #1D1C1C;
}
@media only screen and (max-width: 1919px) {
  .info-area-page-about .funfact-item {
    padding: 29px 30px 31px;
  }
}
@media only screen and (max-width: 1199px) {
  .info-area-page-about .funfact-item {
    padding: 19px 20px 21px;
  }
}
.info-area-page-about .funfact-item .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: #999999;
}
.info-area-page-about .funfact-item .number {
  font-size: 50px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  color: var(--white);
  margin-top: 14px;
}
@media only screen and (max-width: 1199px) {
  .info-area-page-about .funfact-item .number {
    font-size: 35px;
  }
}

/* client area page about style  */
.client-area-page-about {
  background-color: var(--bg);
  margin-bottom: -1px;
}
.client-area-page-about .section-header {
  margin-top: 39px;
}
.client-area-page-about .section-header .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  max-width: 340px;
  color: #FCF7F3;
  margin-inline: auto;
}
.client-area-page-about .clients-wrapper-box {
  margin-top: 63px;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1199px) {
  .client-area-page-about .clients-wrapper-box {
    margin-top: 43px;
  }
}
.client-area-page-about .clients-wrapper {
  display: flex;
  gap: 0;
  justify-content: center;
  flex-wrap: wrap;
}
.client-area-page-about .clients-wrapper .client-slider-active .swiper-slide {
  width: auto;
}
.client-area-page-about .clients-wrapper .client-slider-active .swiper-wrapper {
  transition-timing-function: linear !important;
}
.client-area-page-about .client-box {
  border: 1px solid rgba(252, 247, 243, 0.1);
  border-radius: 70px;
  width: 215px;
  height: 140px;
  padding: 0 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
@media only screen and (max-width: 1919px) {
  .client-area-page-about .client-box {
    width: 155px;
    height: 90px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area-page-about .client-box {
    width: 135px;
    height: 70px;
  }
}

/* media area page about style  */
.media-area-page-about {
  background-color: var(--bg);
}
.media-area-page-about .section-content-wrapper {
  display: grid;
  grid-template-columns: 1fr 575px;
}
@media only screen and (max-width: 1399px) {
  .media-area-page-about .section-content-wrapper {
    grid-template-columns: 1fr 475px;
  }
}
@media only screen and (max-width: 991px) {
  .media-area-page-about .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.media-area-page-about .area-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.media-area-page-about .section-content {
  padding: 87px 50px 100px;
  background-color: #1D1C1C;
}
@media only screen and (max-width: 1399px) {
  .media-area-page-about .section-content {
    padding: 37px 40px 40px;
  }
}
@media only screen and (max-width: 767px) {
  .media-area-page-about .section-content {
    padding: 17px 20px 20px;
  }
}
.media-area-page-about .section-content .section-title {
  font-size: 30px;
  font-weight: 310;
  line-height: 35px;
  letter-spacing: -0.07em;
  color: var(--white);
  max-width: 310px;
}
.media-area-page-about .section-content .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: #999999;
}
.media-area-page-about .section-content .text-wrapper {
  margin-top: 267px;
}
@media only screen and (max-width: 1919px) {
  .media-area-page-about .section-content .text-wrapper {
    margin-top: 167px;
  }
}
@media only screen and (max-width: 1399px) {
  .media-area-page-about .section-content .text-wrapper {
    margin-top: 67px;
  }
}
@media only screen and (max-width: 991px) {
  .media-area-page-about .section-content .text-wrapper {
    margin-top: 27px;
  }
}
.media-area-page-about .section-content .btn-wrapper {
  margin-top: 43px;
}
.media-area-page-about .section-content .rr-btn {
  background-color: #1D1C1C;
  border-color: rgba(255, 255, 255, 0.2);
}
.media-area-page-about .section-content .rr-btn:hover {
  border-color: var(--white);
}
.dark .media-area-page-about .section-content .rr-btn:before {
  background-color: var(--white);
}
.media-area-page-about .section-content .rr-btn .text-one {
  color: var(--white);
}
.media-area-page-about .section-content .rr-btn .text-two {
  color: var(--black);
}
.dark .media-area-page-about .section-content .rr-btn .text-two {
  color: var(--black);
}

/* award area page about style  */
.award-area-page-about {
  background-color: var(--bg);
}
.award-area-page-about .section-subtitle {
  color: var(--white);
}
.award-area-page-about .section-title {
  color: var(--white);
}
.award-area-page-about .section-header {
  margin-top: 50px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .award-area-page-about .section-header {
    margin-top: 10px;
    padding-top: 7px;
  }
}
.award-area-page-about .section-header .subtitle-wrapper {
  margin-top: 8px;
}
.award-area-page-about .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .award-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .award-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .award-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .award-area-page-about .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.award-area-page-about .awards-wrapper-box {
  margin-top: 94px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 32px;
  max-width: 1235px;
  margin-left: auto;
  margin-bottom: 43px;
}
@media only screen and (max-width: 1919px) {
  .award-area-page-about .awards-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .award-area-page-about .awards-wrapper-box {
    margin-top: 44px;
  }
}
.award-area-page-about .awards-wrapper {
  max-width: 630px;
  margin-left: auto;
}
.award-area-page-about .award-box {
  display: grid;
  gap: 20px 30px;
  grid-template-columns: 1fr 370px;
}
@media only screen and (max-width: 767px) {
  .award-area-page-about .award-box {
    grid-template-columns: 1fr 340px;
  }
}
@media (max-width: 575px) {
  .award-area-page-about .award-box {
    grid-template-columns: 1fr;
  }
}
.award-area-page-about .award-box:not(:first-child) {
  margin-top: 56px;
}
.award-area-page-about .award-box .award-list li {
  display: grid;
  gap: 10px 20px;
  grid-template-columns: auto auto;
  justify-content: space-between;
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--white);
}
@media only screen and (max-width: 767px) {
  .award-area-page-about .award-box .award-list li {
    font-size: 18px;
  }
}
.award-area-page-about .award-box .category {
  font-size: 20px;
  font-weight: 400;
  line-height: 20px;
  color: var(--white);
}
@media only screen and (max-width: 767px) {
  .award-area-page-about .award-box .category {
    font-size: 18px;
  }
}

/* team area about page style  */
.team-area-about-page .section-header {
  margin-top: 50px;
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .team-area-about-page .section-header {
    margin-top: 10px;
    padding-top: 7px;
  }
}
.team-area-about-page .section-header .subtitle-wrapper {
  margin-top: 8px;
}
.team-area-about-page .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .team-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .team-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .team-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .team-area-about-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.team-area-about-page .section-title {
  max-width: 765px;
}
@media only screen and (max-width: 1919px) {
  .team-area-about-page .section-title {
    max-width: 665px;
  }
}
@media only screen and (max-width: 1399px) {
  .team-area-about-page .section-title {
    max-width: 465px;
  }
}
.team-area-about-page .team-wrapper-box {
  margin-top: 94px;
}
@media only screen and (max-width: 1919px) {
  .team-area-about-page .team-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .team-area-about-page .team-wrapper-box {
    margin-top: 44px;
  }
}
.team-area-about-page .team-wrapper {
  display: grid;
  gap: 40px 20px;
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 991px) {
  .team-area-about-page .team-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .team-area-about-page .team-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.team-area-about-page .team-box:hover .thumb img {
  transform: scale(1.1);
}
.team-area-about-page .team-box .thumb {
  overflow: hidden;
}
.team-area-about-page .team-box .thumb img {
  width: 100%;
  transition: all 0.5s;
}
.team-area-about-page .team-box .name {
  font-size: 30px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 1919px) {
  .team-area-about-page .team-box .name {
    font-size: 24px;
  }
}
.team-area-about-page .team-box .post {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  display: inline-block;
  margin-top: 3px;
}
.team-area-about-page .team-box .content {
  margin-top: 19px;
}

/* blog details page css */
.blog-details-area .section-title {
  max-width: 1130px;
}
@media only screen and (max-width: 1919px) {
  .blog-details-area .section-title {
    max-width: 930px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-details-area .section-title {
    max-width: 730px;
  }
}
.blog-details-area .section-header {
  margin-top: 17px;
}
@media only screen and (max-width: 1399px) {
  .blog-details-area .section-header {
    margin-top: 37px;
  }
}
.blog-details-area .meta {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 36px;
}
@media only screen and (max-width: 1199px) {
  .blog-details-area .meta {
    margin-top: 9px;
  }
}
.blog-details-area .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 27px;
  display: inline-block;
}
.blog-details-area .meta span.has-left-line {
  padding-inline-start: 15px;
}
.blog-details-area .meta span.has-left-line:before {
  width: 10px;
}
.blog-details-area .meta .name span {
  font-weight: 500;
  color: var(--primary);
}
.blog-details-area .image-wrapper {
  margin-top: 89px;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1919px) {
  .blog-details-area .image-wrapper {
    margin-top: 59px;
    margin-bottom: 40px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-details-area .image-wrapper {
    margin-top: 29px;
    margin-bottom: 30px;
  }
}
.blog-details-area .section-details .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
}
.blog-details-area .section-details .text:not(:first-child) {
  margin-top: 26px;
}
.blog-details-area .section-details .title {
  font-size: 50px;
  font-weight: 310;
  line-height: 0.7;
  letter-spacing: -0.07em;
  margin-bottom: 28px;
}
@media only screen and (max-width: 1919px) {
  .blog-details-area .section-details .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-details-area .section-details .title {
    font-size: 30px;
    margin-bottom: 23px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-details-area .section-details .title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-details-area .section-details .title {
    font-size: 24px;
  }
}
.blog-details-area .section-details .details-info {
  margin-top: 50px;
}
@media only screen and (max-width: 767px) {
  .blog-details-area .section-details .details-info {
    margin-top: 40px;
  }
}
.blog-details-area .section-details .text-wrapper + .details-info {
  margin-top: 43px;
}
@media only screen and (max-width: 767px) {
  .blog-details-area .section-details .text-wrapper + .details-info {
    margin-top: 33px;
  }
}
.blog-details-area .section-details .thumb-text-wrapper {
  margin-top: 17px;
  display: grid;
  gap: 20px 30px;
  grid-template-columns: 1fr 1fr;
}
@media only screen and (max-width: 991px) {
  .blog-details-area .section-details .thumb-text-wrapper {
    grid-template-columns: 1fr;
  }
}
.blog-details-area .section-details .thumb-text-wrapper .thumb {
  margin-top: 6px;
}
.blog-details-area .section-details .thumb-text-wrapper .thumb img {
  width: 100%;
  height: 120%;
  object-fit: cover;
}
.blog-details-area .section-details .feature-list {
  margin-top: 26px;
  margin-bottom: 26px;
}
.blog-details-area .section-details .feature-list li {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  position: relative;
  padding-left: 27px;
}
.blog-details-area .section-details .feature-list li:before {
  position: absolute;
  content: "";
  width: 5px;
  height: 5px;
  background-color: currentColor;
  border-radius: 50%;
  left: 11px;
  top: 10px;
}
.blog-details-area .section-details .gallery-wrapper {
  margin-top: 24px;
  margin-bottom: 20px;
  display: grid;
  gap: 30px;
  grid-template-columns: auto auto;
}
@media only screen and (max-width: 1199px) {
  .blog-details-area .section-details .gallery-wrapper {
    gap: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-details-area .section-details .gallery-wrapper {
    gap: 10px;
  }
}
.blog-details-area .section-details .gallery-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.blog-details-area .section-details .tags-wrapper {
  margin-top: 62px;
  display: flex;
  gap: 10px;
  align-items: flex-start;
}
@media only screen and (max-width: 1399px) {
  .blog-details-area .section-details .tags-wrapper {
    margin-top: 42px;
  }
}
.blog-details-area .section-details .tags-wrapper .heading {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
  display: inline-block;
}
.blog-details-area .section-details .tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.blog-details-area .section-details .tags .tag {
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  display: inline-block;
  padding: 0 15px;
  border: 1px solid var(--border);
  border-radius: 30px;
}
.blog-details-area .section-details .comment-wrap {
  margin-top: 90px;
}
@media only screen and (max-width: 1399px) {
  .blog-details-area .section-details .comment-wrap {
    margin-top: 60px;
  }
}
.blog-details-area .section-details .comment-formwrap {
  display: grid;
  gap: 60px 30px;
  grid-template-columns: repeat(2, 1fr);
  margin-top: 52px;
}
@media only screen and (max-width: 1399px) {
  .blog-details-area .section-details .comment-formwrap {
    gap: 40px 30px;
    margin-top: 32px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-details-area .section-details .comment-formwrap {
    gap: 30px 30px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-details-area .section-details .comment-formwrap {
    grid-template-columns: repeat(1, 1fr);
  }
}
.blog-details-area .section-details .comment-formwrap .message {
  grid-column: span 2;
}
@media only screen and (max-width: 767px) {
  .blog-details-area .section-details .comment-formwrap .message {
    grid-column: auto;
  }
}
.blog-details-area .section-details .comment-formfield input {
  width: 100%;
  height: 40px;
  border: none;
  border-bottom: 1px solid rgba(17, 17, 17, 0.2);
  outline: none;
  background-color: transparent;
  transition: all 0.5s;
  color: var(--primary);
  font-size: 18px;
}
.dark .blog-details-area .section-details .comment-formfield input {
  border-color: rgba(255, 255, 255, 0.2);
}
@media only screen and (max-width: 1199px) {
  .blog-details-area .section-details .comment-formfield input {
    font-size: 16px;
  }
}
.blog-details-area .section-details .comment-formfield input:focus {
  border-color: var(--primary);
}
.blog-details-area .section-details .comment-formfield input::placeholder {
  color: var(--primary);
}
.blog-details-area .section-details .comment-formfield input:-webkit-autofill, .blog-details-area .section-details .comment-formfield input:-webkit-autofill:focus {
  transition: background-color 0s 600000s, color 0s 600000s !important;
}
.blog-details-area .section-details .comment-formfield select {
  width: 100%;
  height: 40px;
  border: none;
  border-bottom: 1px solid var(--primary);
  outline: none;
  background-color: transparent;
  transition: all 0.5s;
  color: var(--primary);
}
.blog-details-area .section-details .comment-formfield select:focus {
  border-color: var(--primary);
}
.blog-details-area .section-details .comment-formfield select option {
  width: 100%;
  max-width: 100%;
}
.blog-details-area .section-details .submit-btn {
  margin-top: 50px;
}
@media only screen and (max-width: 1399px) {
  .blog-details-area .section-details .submit-btn {
    margin-top: 40px;
  }
}

/* blog area 3 style  */
.blog-area-3 .section-header {
  margin-top: 50px;
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .section-header {
    margin-top: 10px;
    padding-top: 7px;
  }
}
.blog-area-3 .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-3 .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.blog-area-3 .subtitle-wrapper {
  margin-top: 8px;
}
.blog-area-3 .section-title {
  max-width: 800px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .section-title {
    max-width: 700px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-3 .section-title {
    max-width: 640px;
  }
}
.blog-area-3 .blogs-wrapper-box {
  margin-top: 94px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blogs-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .blogs-wrapper-box {
    margin-top: 44px;
  }
}
.blog-area-3 .blogs-wrapper {
  display: grid;
  gap: 76px 60px;
  grid-template-columns: repeat(6, 1fr);
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blogs-wrapper {
    gap: 46px 40px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .blogs-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .blog-area-3 .blogs-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.blog-area-3 .blogs-wrapper > * {
  grid-column: span 2;
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .blogs-wrapper > * {
    grid-column: auto;
  }
}
.blog-area-3 .blogs-wrapper > *:nth-child(4), .blog-area-3 .blogs-wrapper > *:nth-child(5) {
  grid-column: span 3;
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .blogs-wrapper > *:nth-child(4), .blog-area-3 .blogs-wrapper > *:nth-child(5) {
    grid-column: auto;
  }
}
.blog-area-3 .blogs-wrapper > *:nth-child(4) .content, .blog-area-3 .blogs-wrapper > *:nth-child(5) .content {
  padding-right: 130px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blogs-wrapper > *:nth-child(4) .content, .blog-area-3 .blogs-wrapper > *:nth-child(5) .content {
    padding-right: 100px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .blogs-wrapper > *:nth-child(4) .content, .blog-area-3 .blogs-wrapper > *:nth-child(5) .content {
    padding-right: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-3 .blogs-wrapper > *:nth-child(4) .content, .blog-area-3 .blogs-wrapper > *:nth-child(5) .content {
    padding-right: 0;
  }
}
.blog-area-3 .blogs-wrapper > * .content {
  padding-right: 90px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blogs-wrapper > * .content {
    padding-right: 70px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .blogs-wrapper > * .content {
    padding-right: 0;
  }
}
.blog-area-3 .blog {
  position: relative;
}
.blog-area-3 .blog:hover .thumb img {
  transform: scale(1.1);
}
.blog-area-3 .blog:hover .title .arrow {
  background-color: var(--primary);
}
.blog-area-3 .blog:hover .title .arrow svg {
  transform: rotate(60deg);
}
.blog-area-3 .blog:hover .title .arrow svg * {
  fill: var(--white);
}
.dark .blog-area-3 .blog:hover .title .arrow svg * {
  fill: var(--black);
}
.blog-area-3 .blog:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--border);
  top: 0;
  left: -30px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blog:before {
    left: -20px;
  }
}
.blog-area-3 .blog .thumb {
  overflow: hidden;
}
.blog-area-3 .blog .thumb img {
  width: 100%;
  transition: all 0.5s;
}
.blog-area-3 .blog .content {
  margin-top: 24px;
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .blog .content {
    margin-top: 14px;
  }
}
.blog-area-3 .blog .title {
  font-size: 36px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  display: inline;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blog .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-3 .blog .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .blog .title {
    font-size: 20px;
  }
}
.blog-area-3 .blog .title .arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  transition: all 0.3s;
  border-radius: 50%;
  border: 2px solid var(--primary);
  transform: translate(0px, -1px);
  margin-left: 5px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blog .title .arrow {
    width: 20px;
    height: 20px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-3 .blog .title .arrow {
    width: 17px;
    height: 17px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .blog .title .arrow {
    width: 15px;
    height: 15px;
    border-width: 1px;
  }
}
.blog-area-3 .blog .title .arrow svg {
  transition: all 0.3s;
  width: 13px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-3 .blog .title .arrow svg {
    width: 10px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-3 .blog .title .arrow svg {
    width: 7px;
  }
}
.blog-area-3 .blog .title .arrow svg * {
  fill: var(--primary);
}
.blog-area-3 .blog .meta {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 14px;
}
@media only screen and (max-width: 1199px) {
  .blog-area-3 .blog .meta {
    margin-top: 9px;
  }
}
.blog-area-3 .blog .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: var(--secondary);
}
.blog-area-3 .blog .meta span.has-left-line {
  padding-inline-start: 15px;
}
.blog-area-3 .blog .meta span.has-left-line:before {
  width: 10px;
}
.blog-area-3 .blog .meta .name span {
  font-weight: 500;
  color: var(--primary);
}

/* blog page css */
.blog-area-2 .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .section-header {
    padding-top: 7px;
  }
}
.blog-area-2 .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-2 .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.blog-area-2 .subtitle-wrapper {
  margin-top: 8px;
}
.blog-area-2 .section-title {
  max-width: 800px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .section-title {
    max-width: 700px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-2 .section-title {
    max-width: 640px;
  }
}
.blog-area-2 .blogs-wrapper-box {
  margin-top: 94px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blogs-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .blogs-wrapper-box {
    margin-top: 44px;
  }
}
.blog-area-2 .blogs-wrapper {
  display: grid;
  gap: 76px 60px;
  grid-template-columns: repeat(6, 1fr);
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blogs-wrapper {
    gap: 46px 40px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .blogs-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .blog-area-2 .blogs-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.blog-area-2 .blogs-wrapper > * {
  grid-column: span 2;
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .blogs-wrapper > * {
    grid-column: auto;
  }
}
.blog-area-2 .blogs-wrapper > *:nth-child(4), .blog-area-2 .blogs-wrapper > *:nth-child(5) {
  grid-column: span 3;
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .blogs-wrapper > *:nth-child(4), .blog-area-2 .blogs-wrapper > *:nth-child(5) {
    grid-column: auto;
  }
}
.blog-area-2 .blogs-wrapper > *:nth-child(4) .content, .blog-area-2 .blogs-wrapper > *:nth-child(5) .content {
  padding-right: 130px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blogs-wrapper > *:nth-child(4) .content, .blog-area-2 .blogs-wrapper > *:nth-child(5) .content {
    padding-right: 100px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .blogs-wrapper > *:nth-child(4) .content, .blog-area-2 .blogs-wrapper > *:nth-child(5) .content {
    padding-right: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .blog-area-2 .blogs-wrapper > *:nth-child(4) .content, .blog-area-2 .blogs-wrapper > *:nth-child(5) .content {
    padding-right: 0;
  }
}
.blog-area-2 .blogs-wrapper > * .content {
  padding-right: 90px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blogs-wrapper > * .content {
    padding-right: 70px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .blogs-wrapper > * .content {
    padding-right: 0;
  }
}
.blog-area-2 .blog {
  position: relative;
}
.blog-area-2 .blog:hover .thumb img {
  transform: scale(1.1);
}
.blog-area-2 .blog:hover .title .arrow {
  background-color: var(--primary);
}
.blog-area-2 .blog:hover .title .arrow svg {
  transform: rotate(60deg);
}
.blog-area-2 .blog:hover .title .arrow svg * {
  fill: var(--white);
}
.dark .blog-area-2 .blog:hover .title .arrow svg * {
  fill: var(--black);
}
.blog-area-2 .blog:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--border);
  top: 0;
  left: -30px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blog:before {
    left: -20px;
  }
}
.blog-area-2 .blog .thumb {
  overflow: hidden;
}
.blog-area-2 .blog .thumb img {
  width: 100%;
  transition: all 0.5s;
}
.blog-area-2 .blog .content {
  margin-top: 24px;
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .blog .content {
    margin-top: 14px;
  }
}
.blog-area-2 .blog .title {
  font-size: 36px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  display: inline;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blog .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-2 .blog .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .blog .title {
    font-size: 20px;
  }
}
.blog-area-2 .blog .title .arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  transition: all 0.3s;
  border-radius: 50%;
  border: 2px solid var(--primary);
  transform: translate(0px, -1px);
  margin-left: 5px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blog .title .arrow {
    width: 20px;
    height: 20px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-2 .blog .title .arrow {
    width: 17px;
    height: 17px;
  }
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .blog .title .arrow {
    width: 15px;
    height: 15px;
    border-width: 1px;
  }
}
.blog-area-2 .blog .title .arrow svg {
  transition: all 0.3s;
  width: 13px;
}
@media only screen and (max-width: 1919px) {
  .blog-area-2 .blog .title .arrow svg {
    width: 10px;
  }
}
@media only screen and (max-width: 1399px) {
  .blog-area-2 .blog .title .arrow svg {
    width: 7px;
  }
}
.blog-area-2 .blog .title .arrow svg * {
  fill: var(--primary);
}
.blog-area-2 .blog .meta {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 14px;
}
@media only screen and (max-width: 1199px) {
  .blog-area-2 .blog .meta {
    margin-top: 9px;
  }
}
.blog-area-2 .blog .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: var(--secondary);
}
.blog-area-2 .blog .meta span.has-left-line {
  padding-inline-start: 15px;
}
.blog-area-2 .blog .meta span.has-left-line:before {
  width: 10px;
}
.blog-area-2 .blog .meta .name span {
  font-weight: 500;
  color: var(--primary);
}

/* contact page css */
/* Loading effect */
.loading-form {
  display: none;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 99;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  color: var(--white);
  font-size: 20px;
  text-align: center;
  padding-top: 20%;
}

.success-message {
  color: green;
  margin-top: 10px;
}

.error-message {
  color: red;
  margin-top: 10px;
}

#response-message {
  margin-top: 10px;
}

.contact-area-contact-page .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .contact-area-contact-page .section-header {
    padding-top: 7px;
  }
}
.contact-area-contact-page .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .contact-area-contact-page .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .contact-area-contact-page .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .contact-area-contact-page .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .contact-area-contact-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.contact-area-contact-page .subtitle-wrapper {
  margin-top: 8px;
}
.contact-area-contact-page .section-title {
  max-width: 875px;
}
@media only screen and (max-width: 1919px) {
  .contact-area-contact-page .section-title {
    max-width: 675px;
  }
}
@media only screen and (max-width: 1399px) {
  .contact-area-contact-page .section-title {
    max-width: 495px;
  }
}
.contact-area-contact-page .section-content-wrapper {
  margin-top: 90px;
  margin-bottom: 4px;
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 1fr 1030px;
  align-items: flex-start;
}
@media only screen and (max-width: 1919px) {
  .contact-area-contact-page .section-content-wrapper {
    margin-top: 60px;
    grid-template-columns: 1fr 730px;
  }
}
@media only screen and (max-width: 1399px) {
  .contact-area-contact-page .section-content-wrapper {
    grid-template-columns: 1fr 680px;
  }
}
@media only screen and (max-width: 1199px) {
  .contact-area-contact-page .section-content-wrapper {
    grid-template-columns: 1fr 550px;
  }
}
@media only screen and (max-width: 991px) {
  .contact-area-contact-page .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.contact-area-contact-page .contact-mail .title {
  font-size: 20px;
  font-weight: 400;
  line-height: 20px;
  color: var(--primary);
}
.contact-area-contact-page .contact-mail .text {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  max-width: 410px;
  margin-top: 24px;
}
@media only screen and (max-width: 1919px) {
  .contact-area-contact-page .contact-mail .text {
    font-size: 24px;
    max-width: 330px;
  }
}
@media only screen and (max-width: 1199px) {
  .contact-area-contact-page .contact-mail .text {
    font-size: 20px;
    max-width: 280px;
  }
}
.contact-area-contact-page .contact-mail .text a {
  text-decoration: underline;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: 1px;
  text-underline-offset: 3px;
}
.contact-area-contact-page .contact-mail .text a:hover {
  color: var(--secondary);
}
.contact-area-contact-page .contact-social {
  margin-top: 51px;
}
.contact-area-contact-page .contact-social .title {
  font-size: 20px;
  font-weight: 400;
  line-height: 20px;
  color: var(--primary);
}
.contact-area-contact-page .contact-social .social-links {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 24px;
}
.contact-area-contact-page .contact-social .social-links a {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  display: inline-block;
  transition: all 0.5s;
}
@media only screen and (max-width: 1919px) {
  .contact-area-contact-page .contact-social .social-links a {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1199px) {
  .contact-area-contact-page .contact-social .social-links a {
    font-size: 20px;
  }
}
.contact-area-contact-page .contact-social .social-links a:hover {
  text-decoration: underline;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: 1px;
  text-underline-offset: 3px;
}
.contact-area-contact-page .contact-formwrap {
  display: grid;
  gap: 60px 60px;
  grid-template-columns: repeat(2, 1fr);
}
@media only screen and (max-width: 1399px) {
  .contact-area-contact-page .contact-formwrap {
    gap: 40px 40px;
  }
}
@media only screen and (max-width: 1199px) {
  .contact-area-contact-page .contact-formwrap {
    gap: 30px 30px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-area-contact-page .contact-formwrap {
    grid-template-columns: repeat(1, 1fr);
  }
}
.contact-area-contact-page .contact-formwrap .message {
  grid-column: span 2;
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .contact-area-contact-page .contact-formwrap .message {
    grid-column: auto;
  }
}
.contact-area-contact-page .contact-formfield input {
  width: 100%;
  height: 40px;
  border: none;
  border-bottom: 1px solid var(--primary);
  outline: none;
  background-color: transparent;
  transition: all 0.5s;
  color: var(--primary);
}
.contact-area-contact-page .contact-formfield input:focus {
  border-color: var(--primary);
}
.contact-area-contact-page .contact-formfield input::placeholder {
  color: var(--primary);
}
.contact-area-contact-page .contact-formfield input:-webkit-autofill, .contact-area-contact-page .contact-formfield input:-webkit-autofill:focus {
  transition: background-color 0s 600000s, color 0s 600000s !important;
}
.contact-area-contact-page .contact-formfield select {
  width: 100%;
  height: 40px;
  border: none;
  border-bottom: 1px solid var(--primary);
  outline: none;
  background-color: transparent;
  transition: all 0.5s;
  color: var(--primary);
}
.contact-area-contact-page .contact-formfield select:focus {
  border-color: var(--primary);
}
.contact-area-contact-page .contact-formfield select option {
  width: 100%;
  max-width: 100%;
}
.dark .contact-area-contact-page .contact-formfield select option {
  background-color: var(--black);
}
.contact-area-contact-page .contact-formfield select option[disabled] {
  background-color: var(--black) !important;
}
.contact-area-contact-page .submit-btn {
  margin-top: 50px;
}
@media only screen and (max-width: 1399px) {
  .contact-area-contact-page .submit-btn {
    margin-top: 40px;
  }
}

/* faq page css */
.faq-area-faq-page .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .faq-area-faq-page .section-header {
    padding-top: 7px;
  }
}
.faq-area-faq-page .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .faq-area-faq-page .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .faq-area-faq-page .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .faq-area-faq-page .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .faq-area-faq-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.faq-area-faq-page .subtitle-wrapper {
  margin-top: 8px;
}
.faq-area-faq-page .section-title {
  max-width: 1005px;
}
@media only screen and (max-width: 1919px) {
  .faq-area-faq-page .section-title {
    max-width: 805px;
  }
}
@media only screen and (max-width: 1399px) {
  .faq-area-faq-page .section-title {
    max-width: 605px;
  }
}
.faq-area-faq-page .accordion-wrapper {
  max-width: 1235px;
  margin-left: auto;
  margin-top: 93px;
  margin-bottom: 10px;
}
@media only screen and (max-width: 1919px) {
  .faq-area-faq-page .accordion-wrapper {
    margin-top: 63px;
    max-width: 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .faq-area-faq-page .accordion-wrapper {
    margin-top: 43px;
    max-width: 900px;
  }
}
@media only screen and (max-width: 1199px) {
  .faq-area-faq-page .accordion-wrapper {
    max-width: 750px;
  }
}
.faq-area-faq-page .accordion {
  border-top: 1px solid var(--border);
  counter-reset: accordion;
}
.faq-area-faq-page .accordion-button {
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  padding: 30px 0 33px;
  border-radius: 0 !important;
  background-color: transparent;
  outline: 0;
  box-shadow: none;
}
@media only screen and (max-width: 1919px) {
  .faq-area-faq-page .accordion-button {
    padding: 20px 0 23px;
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-area-faq-page .accordion-button {
    font-size: 20px;
  }
}
.faq-area-faq-page .accordion-button::after {
  content: "+";
  font-family: var(--font_awesome);
  background-image: none;
  width: auto;
  height: auto;
}
.faq-area-faq-page .accordion-button:not(.collapsed) {
  pointer-events: none;
}
.faq-area-faq-page .accordion-button:not(.collapsed)::after {
  content: "-";
}
.faq-area-faq-page .accordion-item {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid var(--border);
  position: relative;
  padding-left: 130px;
  transition: all 0.5s;
}
@media only screen and (max-width: 991px) {
  .faq-area-faq-page .accordion-item {
    padding-left: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-area-faq-page .accordion-item {
    padding-left: 50px;
  }
}
.faq-area-faq-page .accordion-item:before {
  counter-increment: accordion;
  content: counter(accordion, decimal-leading-zero);
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  position: absolute;
  top: 30px;
  left: 0;
  transition: all 0.5s;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .faq-area-faq-page .accordion-item:before {
    top: 20px;
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-area-faq-page .accordion-item:before {
    font-size: 20px;
  }
}
.faq-area-faq-page .accordion-body {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--secondary);
  padding: 4px 0 43px;
  border: none;
}

/* service details page css */
.hero-area-service-details .service-meta {
  display: grid;
  gap: 10px 60px;
  grid-template-columns: 1fr 1045px;
  position: relative;
  margin-top: 27px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-service-details .service-meta {
    grid-template-columns: 1fr 845px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-service-details .service-meta {
    grid-template-columns: 1fr 645px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-service-details .service-meta {
    grid-template-columns: 1fr 585px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-service-details .service-meta {
    grid-template-columns: 1fr;
  }
}
.hero-area-service-details .service-meta .serial {
  font-size: 18px;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
}
.hero-area-service-details .service-meta .tag {
  font-size: 18px;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
}
.hero-area-service-details .service-meta .next-item {
  font-size: 18px;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
}
.hero-area-service-details .section-header {
  margin-top: 84px;
  display: grid;
  grid-template-columns: 1045px;
  justify-content: flex-end;
}
@media only screen and (max-width: 1919px) {
  .hero-area-service-details .section-header {
    grid-template-columns: 845px;
    margin-top: 64px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-service-details .section-header {
    grid-template-columns: 645px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-service-details .section-header {
    grid-template-columns: 585px;
    margin-top: 44px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-service-details .section-header {
    grid-template-columns: 1fr;
  }
}
.hero-area-service-details .section-content-wrapper {
  margin-top: 94px;
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 1fr 1045px;
}
@media only screen and (max-width: 1919px) {
  .hero-area-service-details .section-content-wrapper {
    grid-template-columns: 1fr 845px;
    margin-top: 64px;
  }
}
@media only screen and (max-width: 1399px) {
  .hero-area-service-details .section-content-wrapper {
    grid-template-columns: 1fr 645px;
  }
}
@media only screen and (max-width: 1199px) {
  .hero-area-service-details .section-content-wrapper {
    grid-template-columns: 1fr 585px;
    margin-top: 44px;
  }
}
@media only screen and (max-width: 991px) {
  .hero-area-service-details .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.hero-area-service-details .section-content .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  max-width: 420px;
}
.hero-area-service-details .section-thumb img {
  width: 100%;
}
.hero-area-service-details .feature-list {
  margin-top: 26px;
}
.hero-area-service-details .feature-list li {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: var(--primary);
  display: flex;
  align-items: center;
}
.hero-area-service-details .feature-list li:before {
  content: "+";
  margin-right: 5px;
}

/* approach area service details style  */
.approach-area-service-details-page .section-header {
  margin-top: 32px;
}
.approach-area-service-details-page .section-title-wrapper {
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 1fr 1125px;
  align-items: flex-end;
}
@media only screen and (max-width: 1919px) {
  .approach-area-service-details-page .section-title-wrapper {
    grid-template-columns: 1fr 905px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-service-details-page .section-title-wrapper {
    grid-template-columns: 1fr 675px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area-service-details-page .section-title-wrapper {
    grid-template-columns: 1fr 575px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area-service-details-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.approach-area-service-details-page .section-subtitle {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  text-transform: unset;
}
@media only screen and (max-width: 1919px) {
  .approach-area-service-details-page .section-subtitle {
    font-size: 24px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area-service-details-page .section-subtitle {
    font-size: 18px;
  }
  .approach-area-service-details-page .section-subtitle br {
    display: none;
  }
}
.approach-area-service-details-page .section-title {
  max-width: 875px;
}
@media (min-width: 1200px) {
  .approach-area-service-details-page .section-title {
    font-size: 50px;
    line-height: 1;
  }
}
.approach-area-service-details-page .approach-wrapper-box {
  margin-top: 94px;
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 1fr 1125px;
  align-items: flex-start;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1919px) {
  .approach-area-service-details-page .approach-wrapper-box {
    grid-template-columns: 1fr 905px;
    margin-top: 64px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-service-details-page .approach-wrapper-box {
    grid-template-columns: 1fr 675px;
  }
}
@media only screen and (max-width: 1199px) {
  .approach-area-service-details-page .approach-wrapper-box {
    grid-template-columns: 1fr 575px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area-service-details-page .approach-wrapper-box {
    grid-template-columns: 1fr;
    margin-top: 44px;
  }
}
.approach-area-service-details-page .approach-wrapper-box .steps {
  font-family: var(--font_sequelsansromanbody);
  font-size: 265px;
  font-weight: 310;
  line-height: 0.65;
  letter-spacing: -0.07em;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .approach-area-service-details-page .approach-wrapper-box .steps {
    font-size: 205px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-service-details-page .approach-wrapper-box .steps {
    font-size: 165px;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area-service-details-page .approach-wrapper-box .steps {
    display: none;
  }
}
.approach-area-service-details-page .approach-wrapper {
  border-top: 1px dashed #878482;
}
.approach-area-service-details-page .approach-box {
  display: grid;
  gap: 10px 50px;
  grid-template-columns: 60px 1fr 595px;
  align-items: flex-start;
  padding-top: 24px;
  padding-bottom: 24px;
  border-bottom: 1px dashed #878482;
}
@media only screen and (max-width: 1919px) {
  .approach-area-service-details-page .approach-box {
    grid-template-columns: 60px 1fr 395px;
  }
}
@media only screen and (max-width: 1399px) {
  .approach-area-service-details-page .approach-box {
    grid-template-columns: 60px 1fr;
  }
}
@media only screen and (max-width: 991px) {
  .approach-area-service-details-page .approach-box {
    gap: 10px 30px;
  }
}
@media (max-width: 575px) {
  .approach-area-service-details-page .approach-box {
    grid-template-columns: 30px 1fr;
  }
}
.approach-area-service-details-page .approach-box .number {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: var(--primary);
}
@media only screen and (max-width: 1399px) {
  .approach-area-service-details-page .approach-box .number {
    grid-row: span 2;
  }
}
.approach-area-service-details-page .approach-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 30px;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 1919px) {
  .approach-area-service-details-page .approach-box .title {
    font-size: 24px;
  }
}
.approach-area-service-details-page .approach-box .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
}

/* feature area style  */
.feature-area {
  background-color: var(--bg);
}
.feature-area .features-wrapper-box {
  margin-top: 44px;
}
.feature-area .features-wrapper {
  display: grid;
  gap: 60px 60px;
  grid-template-columns: repeat(4, 1fr);
  overflow: hidden;
}
@media only screen and (max-width: 1919px) {
  .feature-area .features-wrapper {
    gap: 60px 40px;
  }
}
@media only screen and (max-width: 1199px) {
  .feature-area .features-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 767px) {
  .feature-area .features-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.feature-area .features-wrapper > *:nth-child(2n) .thumb {
  order: 2;
}
@media only screen and (max-width: 1199px) {
  .feature-area .features-wrapper > *:nth-child(2n) .thumb {
    order: unset;
  }
}
.feature-area .feature-box {
  position: relative;
  display: grid;
  gap: 175px;
}
@media only screen and (max-width: 1919px) {
  .feature-area .feature-box {
    gap: 95px;
  }
}
@media only screen and (max-width: 1399px) {
  .feature-area .feature-box {
    gap: 75px;
  }
}
@media only screen and (max-width: 1199px) {
  .feature-area .feature-box {
    gap: 45px;
  }
}
.feature-area .feature-box:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  top: 0;
  left: -30px;
}
@media only screen and (max-width: 1919px) {
  .feature-area .feature-box:before {
    left: -20px;
  }
}
.feature-area .feature-box .thumb img {
  height: 80px;
}
@media only screen and (max-width: 1919px) {
  .feature-area .feature-box .thumb img {
    height: 60px;
  }
}
.feature-area .feature-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  color: var(--white);
}
@media only screen and (max-width: 1919px) {
  .feature-area .feature-box .title {
    font-size: 24px;
  }
}
.feature-area .feature-box .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--white-2);
  margin-top: 30px;
}
@media only screen and (max-width: 1919px) {
  .feature-area .feature-box .text {
    font-size: 18px;
    margin-top: 20px;
  }
}

/* value area style  */
.value-area {
  background-color: var(--bg);
}
.value-area .section-content-wrapper {
  margin-top: 32px;
  margin-bottom: 45px;
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 605px 905px;
  justify-content: space-between;
  align-items: flex-start;
}
@media only screen and (max-width: 1919px) {
  .value-area .section-content-wrapper {
    grid-template-columns: 1fr 770px;
  }
}
@media only screen and (max-width: 1199px) {
  .value-area .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.value-area .section-thumb {
  margin-top: 11px;
  max-width: 660px;
}
.value-area .section-thumb img {
  width: 100%;
}
.value-area .section-title {
  letter-spacing: -0.07em;
  color: var(--white);
  max-width: 660px;
}
@media (min-width: 1200px) {
  .value-area .section-title {
    font-size: 50px;
    font-weight: 315;
    line-height: 55px;
  }
}
.value-area .values-wrapper {
  margin-top: 56px;
}
@media only screen and (max-width: 991px) {
  .value-area .values-wrapper {
    margin-top: 36px;
  }
}
.value-area .value-box {
  display: grid;
  gap: 20px 80px;
  grid-template-columns: 330px 1fr;
}
@media only screen and (max-width: 1919px) {
  .value-area .value-box {
    grid-template-columns: 230px 1fr;
  }
}
@media only screen and (max-width: 991px) {
  .value-area .value-box {
    grid-template-columns: 130px 1fr;
  }
}
@media only screen and (max-width: 767px) {
  .value-area .value-box {
    grid-template-columns: 1fr;
  }
}
.value-area .value-box:not(:first-child) {
  margin-top: 68px;
}
@media only screen and (max-width: 991px) {
  .value-area .value-box:not(:first-child) {
    margin-top: 38px;
  }
}
.value-area .value-box .number {
  font-size: 100px;
  font-weight: 310;
  line-height: 0.9;
  letter-spacing: -0.07em;
  color: var(--white);
  padding-top: 17px;
  border-top: 1px solid rgba(252, 247, 243, 0.2);
  margin-top: 6px;
}
@media only screen and (max-width: 1919px) {
  .value-area .value-box .number {
    font-size: 80px;
  }
}
@media only screen and (max-width: 1399px) {
  .value-area .value-box .number {
    font-size: 60px;
  }
}
@media only screen and (max-width: 1199px) {
  .value-area .value-box .number {
    font-size: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .value-area .value-box .number {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .value-area .value-box .number {
    font-size: 35px;
  }
}
.value-area .value-box .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  color: var(--white-2);
}
@media only screen and (max-width: 1919px) {
  .value-area .value-box .text {
    font-size: 18px;
  }
}

/* faq area style  */
.faq-area .section-header {
  margin-top: 50px;
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .faq-area .section-header {
    margin-top: 10px;
    padding-top: 7px;
  }
}
.faq-area .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .faq-area .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .faq-area .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .faq-area .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .faq-area .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.faq-area .subtitle-wrapper {
  margin-top: 8px;
}
.faq-area .section-title {
  max-width: 1005px;
}
@media only screen and (max-width: 1919px) {
  .faq-area .section-title {
    max-width: 905px;
  }
}
@media only screen and (max-width: 1399px) {
  .faq-area .section-title {
    max-width: 705px;
  }
}
.faq-area .accordion-wrapper {
  max-width: 1235px;
  margin-left: auto;
  margin-top: 93px;
  margin-bottom: 10px;
}
@media only screen and (max-width: 1919px) {
  .faq-area .accordion-wrapper {
    margin-top: 63px;
    max-width: 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .faq-area .accordion-wrapper {
    margin-top: 43px;
    max-width: 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .faq-area .accordion-wrapper {
    max-width: 750px;
  }
}
.faq-area .accordion {
  border-top: 1px solid var(--border);
  counter-reset: accordion;
}
.faq-area .accordion-button {
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  padding: 30px 0 33px;
  border-radius: 0 !important;
  background-color: transparent;
  outline: 0;
  box-shadow: none;
}
@media only screen and (max-width: 1919px) {
  .faq-area .accordion-button {
    padding: 20px 0 23px;
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-area .accordion-button {
    font-size: 20px;
  }
}
.faq-area .accordion-button::after {
  content: "+";
  font-family: var(--font_awesome);
  background-image: none;
  width: auto;
  height: auto;
}
.faq-area .accordion-button:not(.collapsed) {
  pointer-events: none;
}
.faq-area .accordion-button:not(.collapsed)::after {
  content: "-";
}
.faq-area .accordion-item {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid var(--border);
  position: relative;
  padding-left: 130px;
  transition: all 0.5s;
}
@media only screen and (max-width: 991px) {
  .faq-area .accordion-item {
    padding-left: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-area .accordion-item {
    padding-left: 50px;
  }
}
.faq-area .accordion-item:before {
  counter-increment: accordion;
  content: counter(accordion, decimal-leading-zero);
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 1.16;
  letter-spacing: -0.07em;
  color: var(--primary);
  position: absolute;
  top: 30px;
  left: 0;
  transition: all 0.5s;
}
@media only screen and (max-width: 1919px) {
  .faq-area .accordion-item:before {
    top: 20px;
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .faq-area .accordion-item:before {
    font-size: 20px;
  }
}
.faq-area .accordion-body {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--secondary);
  padding: 4px 0 43px;
  border: none;
}

/* sercices page css */
.service-area-service-page .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .service-area-service-page .section-header {
    padding-top: 7px;
  }
}
.service-area-service-page .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .service-area-service-page .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-service-page .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .service-area-service-page .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .service-area-service-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.service-area-service-page .subtitle-wrapper {
  margin-top: 8px;
}
.service-area-service-page .section-title {
  max-width: 800px;
}
@media only screen and (max-width: 1919px) {
  .service-area-service-page .section-title {
    max-width: 700px;
  }
}
@media only screen and (max-width: 1399px) {
  .service-area-service-page .section-title {
    max-width: 500px;
  }
}
.service-area-service-page .services-wrapper-box {
  margin-top: 94px;
}
@media only screen and (max-width: 1919px) {
  .service-area-service-page .services-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .service-area-service-page .services-wrapper-box {
    margin-top: 44px;
  }
}
.service-area-service-page .service-content-wrapper {
  background-color: transparent;
}
.service-area-service-page .service-content-wrapper .service-content .text.text-invert > div {
  background-image: linear-gradient(to right, rgb(17, 17, 17) 50%, rgba(17, 17, 17, 0.3) 51%);
}
.dark .service-area-service-page .service-content-wrapper .service-content .text.text-invert > div {
  background-image: linear-gradient(to right, rgb(255, 255, 255) 50%, rgba(255, 255, 255, 0.3) 51%);
}
.service-area-service-page .services-wrapper-2 .service-box {
  background-color: var(--white);
  border-color: var(--border);
}
.dark .service-area-service-page .services-wrapper-2 .service-box {
  background-color: var(--black);
}
.service-area-service-page .services-wrapper-2 .service-box:last-child {
  border-color: var(--border);
}
.service-area-service-page .services-wrapper-2 .service-box-wrapper {
  background-color: var(--border);
}
.service-area-service-page .services-wrapper-2 .service-box .number {
  color: var(--primary);
}
.service-area-service-page .services-wrapper-2 .service-box .title {
  color: var(--primary);
}
.service-area-service-page .services-wrapper-2 .service-box .text {
  color: var(--primary);
}
.service-area-service-page .capabilities-area-2 .capability-box .title {
  font-family: var(--font_thunder);
  font-size: 100px;
  font-weight: 400;
  line-height: 0.85;
  text-transform: uppercase;
}

/* client area service-page style  */
.client-area-service-page .section-title {
  max-width: 1430px;
}
@media only screen and (max-width: 1919px) {
  .client-area-service-page .section-title {
    max-width: 1130px;
  }
}
.client-area-service-page .section-title span {
  color: var(--primary);
}
.client-area-service-page .section-content {
  margin-top: 20px;
}
.client-area-service-page .section-content .text-wrapper {
  max-width: 505px;
  margin-top: 133px;
  margin-left: 545px;
}
@media only screen and (max-width: 1919px) {
  .client-area-service-page .section-content .text-wrapper {
    margin-top: 83px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area-service-page .section-content .text-wrapper {
    margin-top: 63px;
    margin-left: 345px;
  }
}
@media only screen and (max-width: 991px) {
  .client-area-service-page .section-content .text-wrapper {
    margin-top: 43px;
    margin-left: auto;
  }
}
@media only screen and (max-width: 767px) {
  .client-area-service-page .section-content .text-wrapper {
    max-width: 100%;
    margin-top: 23px;
  }
}
.client-area-service-page .client-capsule-wrapper {
  position: relative;
  overflow: hidden;
  pointer-events: none;
  margin-top: -200px;
  height: 633px;
}
@media only screen and (max-width: 1919px) {
  .client-area-service-page .client-capsule-wrapper {
    height: 533px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area-service-page .client-capsule-wrapper {
    height: 483px;
  }
}
@media only screen and (max-width: 991px) {
  .client-area-service-page .client-capsule-wrapper {
    height: 433px;
  }
}
.client-area-service-page .client-capsule-wrapper > * {
  position: absolute;
  display: inline-block;
  margin-bottom: 0;
  left: 0;
  top: 0;
  user-select: none;
  pointer-events: auto;
  transition: none;
}
.client-area-service-page .client-box {
  width: 215px;
  height: 100px;
  padding: 10px 20px;
  background-color: var(--primary);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  transform: translate(-50%, -50%) rotate(0rad);
}
@media only screen and (max-width: 1919px) {
  .client-area-service-page .client-box {
    width: 165px;
    height: 70px;
  }
}
@media only screen and (max-width: 1399px) {
  .client-area-service-page .client-box {
    width: 135px;
    height: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .client-area-service-page .client-box {
    width: 105px;
    height: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .client-area-service-page .client-box {
    width: 85px;
    height: 30px;
  }
}
.client-area-service-page .client-box img {
  pointer-events: none;
  max-width: 100%;
  max-height: 100%;
}
.client-area-service-page .line {
  border-bottom: 1px solid var(--primary);
}
.client-area-service-page .lines-wrapper {
  display: grid;
  gap: 5px 0;
}
@media only screen and (max-width: 1399px) {
  .client-area-service-page .lines-wrapper {
    gap: 3px 0;
  }
}
@media only screen and (max-width: 767px) {
  .client-area-service-page .lines-wrapper {
    gap: 1px 0;
  }
}

/* team details page css */
.team-details-area .section-content-wrapper {
  margin-top: 17px;
  display: grid;
  gap: 40px 60px;
  grid-template-columns: 590px 600px;
  justify-content: space-between;
}
@media only screen and (max-width: 1399px) {
  .team-details-area .section-content-wrapper {
    grid-template-columns: 1fr 500px;
  }
}
@media only screen and (max-width: 1199px) {
  .team-details-area .section-content-wrapper {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 991px) {
  .team-details-area .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.team-details-area .team-thumb {
  margin-top: 13px;
  order: 2;
}
@media only screen and (max-width: 991px) {
  .team-details-area .team-thumb {
    order: unset;
    max-width: 500px;
  }
}
.team-details-area .team-thumb img {
  width: 100%;
}
.team-details-area .section-subtitle {
  font-family: var(--font_sequelsansromanbody);
  font-size: 30px;
  font-weight: 310;
  line-height: 0.93;
  letter-spacing: -0.07em;
  color: var(--primary);
  text-transform: unset;
}
@media only screen and (max-width: 1399px) {
  .team-details-area .section-subtitle {
    font-size: 24px;
  }
}
@media only screen and (max-width: 991px) {
  .team-details-area .section-subtitle {
    font-size: 22px;
  }
}
.team-details-area .subtitle-wrapper {
  margin-top: 29px;
}
@media only screen and (max-width: 1399px) {
  .team-details-area .subtitle-wrapper {
    margin-top: 19px;
  }
}
.team-details-area .section-content .text-wrapper {
  margin-top: 51px;
}
@media only screen and (max-width: 1399px) {
  .team-details-area .section-content .text-wrapper {
    margin-top: 41px;
  }
}
@media only screen and (max-width: 991px) {
  .team-details-area .section-content .text-wrapper {
    margin-top: 31px;
  }
}
.team-details-area .section-content .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
}
.team-details-area .section-content .text:not(:first-child) {
  margin-top: 28px;
}
.team-details-area .social-links {
  margin-top: 53px;
  border-top: 1px dashed #878482;
  display: grid;
}
.dark .team-details-area .social-links {
  border-color: #6F6D6C;
}
@media only screen and (max-width: 1399px) {
  .team-details-area .social-links {
    margin-top: 43px;
  }
}
@media only screen and (max-width: 991px) {
  .team-details-area .social-links {
    margin-top: 33px;
  }
}
.team-details-area .social-links a {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #878482;
  padding: 11px 0;
  transition: all 0.5s;
}
.dark .team-details-area .social-links a {
  border-color: #6F6D6C;
}
.team-details-area .social-links a:hover {
  background-color: #F7F7FA;
  padding-left: 20px;
}
.dark .team-details-area .social-links a:hover {
  background-color: #171717;
}
.team-details-area .social-links a:before {
  content: "+";
  margin-right: 6px;
}

/* team area team page style  */
.team-area-team-page .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .team-area-team-page .section-header {
    padding-top: 7px;
  }
}
.team-area-team-page .section-title-wrapper {
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 325px 1fr;
}
@media only screen and (max-width: 1919px) {
  .team-area-team-page .section-title-wrapper {
    grid-template-columns: 275px 1fr;
  }
}
@media only screen and (max-width: 991px) {
  .team-area-team-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.team-area-team-page .subtitle-wrapper {
  margin-top: 8px;
}
.team-area-team-page .section-title {
  max-width: 530px;
}
@media only screen and (max-width: 1919px) {
  .team-area-team-page .section-title {
    max-width: 430px;
  }
}
@media only screen and (max-width: 1399px) {
  .team-area-team-page .section-title {
    max-width: 350px;
  }
}
.team-area-team-page .team-info {
  margin-top: 13px;
}
.team-area-team-page .team-info .team-group {
  display: inline-flex;
  align-items: center;
}
.team-area-team-page .team-info .team-group img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  outline: 3px solid var(--white);
}
.dark .team-area-team-page .team-info .team-group img {
  outline-color: var(--black);
}
@media only screen and (max-width: 1919px) {
  .team-area-team-page .team-info .team-group img {
    width: 50px;
    height: 50px;
  }
}
.team-area-team-page .team-info .team-group img:not(:first-child) {
  margin-left: -10px;
}
.team-area-team-page .team-info .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 20px;
  max-width: 275px;
}
.team-area-team-page .team-info .text span {
  font-weight: 500;
  color: var(--primary);
}
.team-area-team-page .team-info .text-wrapper {
  margin-top: 16px;
}
.team-area-team-page .title-wrapper .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  max-width: 580px;
  margin-top: 54px;
}
@media only screen and (max-width: 1919px) {
  .team-area-team-page .title-wrapper .text {
    margin-top: 44px;
  }
}
@media only screen and (max-width: 1399px) {
  .team-area-team-page .title-wrapper .text {
    margin-top: 24px;
  }
}

/* cta area team page style  */
.cta-area-team-page .section-content {
  margin-top: 32px;
  text-align: center;
}
.cta-area-team-page .section-content .btn-wrapper {
  margin-top: 45px;
  margin-bottom: 1px;
}
@media only screen and (max-width: 1199px) {
  .cta-area-team-page .section-content .btn-wrapper {
    margin-top: 35px;
  }
}
.cta-area-team-page .section-title {
  max-width: 920px;
  margin-inline: auto;
}
@media (min-width: 1200px) {
  .cta-area-team-page .section-title {
    font-size: 50px;
    line-height: 50px;
  }
}

/* team list area style  */
.team-list-area .team-box {
  border-bottom: 1px solid #E1E1E1;
  padding-top: 20px;
  padding-bottom: 20px;
  display: grid;
  gap: 15px 25px;
  grid-template-columns: 100px 1fr 960px auto;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s;
}
.dark .team-list-area .team-box {
  border-color: #292929;
}
@media only screen and (max-width: 1919px) {
  .team-list-area .team-box {
    grid-template-columns: 100px 1fr 560px auto;
  }
}
@media only screen and (max-width: 1199px) {
  .team-list-area .team-box {
    grid-template-columns: 100px 1fr 310px auto;
  }
}
@media only screen and (max-width: 991px) {
  .team-list-area .team-box {
    grid-template-columns: 80px 1fr 210px auto;
  }
}
@media only screen and (max-width: 767px) {
  .team-list-area .team-box {
    grid-template-columns: 1fr 1fr auto;
  }
}
.team-list-area .team-box:hover {
  background-color: #FAFAFA;
}
.dark .team-list-area .team-box:hover {
  background-color: #1D1C1C;
}
.team-list-area .team-box:hover .thumb {
  transform: translateX(20px);
}
.team-list-area .team-box:hover .name {
  transform: translateX(20px);
}
.team-list-area .team-box:hover .t-btn-normal {
  transform: translateX(-20px);
}
.team-list-area .team-box .thumb {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.5s;
}
@media only screen and (max-width: 991px) {
  .team-list-area .team-box .thumb {
    width: 80px;
    height: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .team-list-area .team-box .thumb {
    grid-column: span 3;
  }
}
.team-list-area .team-box .thumb img {
  width: 100%;
}
.team-list-area .team-box .name {
  font-size: 30px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  transition: all 0.5s;
}
@media only screen and (max-width: 1399px) {
  .team-list-area .team-box .name {
    font-size: 24px;
  }
}
.team-list-area .team-box .post {
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  display: inline-block;
}
.team-list-area .team-wrapper-box {
  margin-top: 90px;
}
@media only screen and (max-width: 1399px) {
  .team-list-area .team-wrapper-box {
    margin-top: 70px;
  }
}
.team-list-area .team-wrapper {
  border-top: 1px solid #E1E1E1;
}
.dark .team-list-area .team-wrapper {
  border-color: #292929;
}

/* team area style  */
.team-area .section-header {
  margin-top: 59px;
  display: grid;
  gap: 20px 20px;
  grid-template-columns: 1fr 1fr;
  align-items: flex-end;
}
@media only screen and (max-width: 991px) {
  .team-area .section-header {
    grid-template-columns: 1fr;
  }
}
.team-area .section-header .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  max-width: 375px;
}
.team-area .section-header .section-title-wrapper {
  order: 2;
}
@media only screen and (max-width: 991px) {
  .team-area .section-header .section-title-wrapper {
    order: unset;
  }
}
.team-area .team-wrapper-box {
  margin-top: 94px;
}
@media only screen and (max-width: 1919px) {
  .team-area .team-wrapper-box {
    margin-top: 64px;
  }
}
@media only screen and (max-width: 991px) {
  .team-area .team-wrapper-box {
    margin-top: 44px;
  }
}
.team-area .team-wrapper {
  display: grid;
  gap: 40px 20px;
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 991px) {
  .team-area .team-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .team-area .team-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.team-area .team-box:hover .thumb img {
  transform: scale(1.1);
}
.team-area .team-box .thumb {
  overflow: hidden;
}
.team-area .team-box .thumb img {
  width: 100%;
  transition: all 0.5s;
}
.team-area .team-box .name {
  font-size: 30px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
}
@media only screen and (max-width: 1919px) {
  .team-area .team-box .name {
    font-size: 24px;
  }
}
.team-area .team-box .post {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  display: inline-block;
  margin-top: 3px;
}
.team-area .team-box .content {
  margin-top: 19px;
}

/* work details page css */
.work-details-area .section-header {
  margin-top: 17px;
}
.work-details-area .section-title-wrapper {
  display: grid;
  gap: 20px 29px;
  grid-template-columns: 315px 1fr;
}
@media only screen and (max-width: 1919px) {
  .work-details-area .section-title-wrapper {
    grid-template-columns: 245px 1fr;
  }
}
@media only screen and (max-width: 1399px) {
  .work-details-area .section-title-wrapper {
    grid-template-columns: 195px 1fr;
  }
}
@media only screen and (max-width: 1199px) {
  .work-details-area .section-title-wrapper {
    align-items: center;
  }
}
@media only screen and (max-width: 767px) {
  .work-details-area .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.work-details-area .section-title-wrapper .title-thumb {
  border-radius: 20px;
  overflow: hidden;
  display: inline-block;
  margin-top: 13px;
  max-width: 315px;
}
@media only screen and (max-width: 1399px) {
  .work-details-area .section-title-wrapper .title-thumb {
    margin-top: 5px;
  }
}
.work-details-area .meta-wrapper {
  margin-top: 51px;
  display: grid;
  gap: 30px 60px;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  margin-bottom: 95px;
}
@media only screen and (max-width: 1919px) {
  .work-details-area .meta-wrapper {
    margin-top: 41px;
    margin-bottom: 65px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-details-area .meta-wrapper {
    gap: 30px 40px;
    margin-top: 31px;
    margin-bottom: 45px;
  }
}
@media only screen and (max-width: 991px) {
  .work-details-area .meta-wrapper {
    grid-template-columns: 1fr 1fr;
  }
}
.work-details-area .meta-item {
  border-top: 1px solid var(--border);
  padding-top: 14px;
}
.work-details-area .meta-item .title {
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
}
.work-details-area .meta-item .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  color: var(--primary);
  margin-top: 4px;
}
.work-details-area .section-info {
  margin-top: 59px;
  margin-bottom: 93px;
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 1fr 825px;
}
@media only screen and (max-width: 1919px) {
  .work-details-area .section-info {
    margin-top: 49px;
    margin-bottom: 63px;
  }
}
@media only screen and (max-width: 1399px) {
  .work-details-area .section-info {
    grid-template-columns: 1fr 650px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-details-area .section-info {
    grid-template-columns: 1fr 550px;
    margin-top: 29px;
    margin-bottom: 43px;
  }
}
@media only screen and (max-width: 991px) {
  .work-details-area .section-info {
    grid-template-columns: 1fr;
  }
}
.work-details-area .section-info .title {
  font-size: 50px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.07em;
  max-width: 440px;
}
@media only screen and (max-width: 1919px) {
  .work-details-area .section-info .title {
    font-size: 38px;
    max-width: 340px;
  }
}
@media (max-width: 575px) {
  .work-details-area .section-info .title {
    font-size: 28px;
  }
}
.work-details-area .section-info .content {
  margin-top: 2px;
}
.work-details-area .section-info .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
}
.work-details-area .section-info .feature-list {
  margin-top: 28px;
}
.work-details-area .section-info .feature-list li {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  color: var(--primary);
  position: relative;
  display: flex;
  align-items: center;
}
.work-details-area .section-info .feature-list li:before {
  content: "+";
  margin-right: 6px;
}
.work-details-area .gallery-wrapper {
  padding: 0 50px;
  display: grid;
  gap: 10px;
  grid-template-columns: 1fr 1fr 1fr;
}
@media only screen and (max-width: 1199px) {
  .work-details-area .gallery-wrapper {
    padding: 0 10px;
  }
}
.work-details-area .gallery-wrapper > *:nth-child(1) {
  grid-column: span 2;
}
.work-details-area .gallery-wrapper > *:nth-child(3) {
  grid-column: span 3;
}
.work-details-area .gallery-wrapper .image {
  border-radius: 20px;
}
@media only screen and (max-width: 767px) {
  .work-details-area .gallery-wrapper .image {
    border-radius: 5px;
  }
}
.work-details-area .details-info .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 27px;
  letter-spacing: -0.08em;
}
@media only screen and (max-width: 1199px) {
  .work-details-area .details-info .title {
    font-size: 24px;
  }
}
.work-details-area .details-info .text {
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  margin-top: 16px;
}
@media only screen and (max-width: 1199px) {
  .work-details-area .details-info .text {
    font-size: 18px;
  }
}
.work-details-area .section-details {
  margin-top: 41px;
  margin-bottom: 93px;
  display: grid;
  gap: 30px 60px;
  grid-template-columns: 1fr 1fr;
  max-width: 1120px;
  margin-left: auto;
}
@media only screen and (max-width: 1919px) {
  .work-details-area .section-details {
    margin-top: 41px;
    margin-bottom: 63px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-details-area .section-details {
    margin-top: 31px;
    margin-bottom: 43px;
  }
}
@media only screen and (max-width: 767px) {
  .work-details-area .section-details {
    grid-template-columns: 1fr;
  }
}
.work-details-area .gallery-wrapper-2 {
  padding: 0 50px;
  display: grid;
  gap: 10px;
  grid-template-columns: 1fr;
}
@media only screen and (max-width: 1199px) {
  .work-details-area .gallery-wrapper-2 {
    padding: 0 10px;
  }
}
.work-details-area .gallery-wrapper-2 .image {
  border-radius: 20px;
}
@media only screen and (max-width: 767px) {
  .work-details-area .gallery-wrapper-2 .image {
    border-radius: 5px;
  }
}
.work-details-area .pagination {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 1px;
}
.work-details-area .pagination a {
  border: 1px solid var(--border);
  width: 170px;
  height: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  border-radius: 90px;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .work-details-area .pagination a {
    width: 150px;
    height: 70px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-details-area .pagination a {
    width: 120px;
    height: 50px;
  }
}
.work-details-area .pagination a:hover {
  background-color: var(--primary);
  border-color: transparent;
  color: var(--white);
}
.dark .work-details-area .pagination a:hover {
  color: var(--black);
}
.work-details-area .pagination a:hover svg * {
  fill: var(--white);
}
.dark .work-details-area .pagination a:hover svg * {
  fill: var(--black);
}
.work-details-area .pagination a svg * {
  fill: var(--primary);
}

/* work page css */
.work-area-work-page .section-header {
  border-top: 1px solid var(--border);
  padding-top: 37px;
}
@media only screen and (max-width: 991px) {
  .work-area-work-page .section-header {
    padding-top: 7px;
  }
}
.work-area-work-page .section-title-wrapper {
  display: grid;
  gap: 15px 60px;
  grid-template-columns: 1fr 1235px;
}
@media only screen and (max-width: 1919px) {
  .work-area-work-page .section-title-wrapper {
    grid-template-columns: 1fr 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .work-area-work-page .section-title-wrapper {
    grid-template-columns: 1fr 850px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-work-page .section-title-wrapper {
    grid-template-columns: 1fr 750px;
  }
}
@media only screen and (max-width: 991px) {
  .work-area-work-page .section-title-wrapper {
    grid-template-columns: 1fr;
  }
}
.work-area-work-page .subtitle-wrapper {
  margin-top: 8px;
}
.work-area-work-page .section-title {
  max-width: 800px;
}
@media only screen and (max-width: 1919px) {
  .work-area-work-page .section-title {
    max-width: 700px;
  }
}
@media only screen and (max-width: 1399px) {
  .work-area-work-page .section-title {
    max-width: 640px;
  }
}
.work-area-work-page .section-content .text {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  max-width: 490px;
}
.work-area-work-page .info-list li {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  position: relative;
  display: flex;
  align-items: center;
}
.work-area-work-page .info-list li:before {
  content: "+";
  margin-right: 5px;
}
.work-area-work-page .section-content-wrapper {
  margin-top: 84px;
  margin-bottom: 93px;
  max-width: 1235px;
  margin-left: auto;
  display: grid;
  gap: 20px 60px;
  grid-template-columns: 200px 1fr;
}
@media only screen and (max-width: 1919px) {
  .work-area-work-page .section-content-wrapper {
    margin-top: 54px;
    margin-bottom: 53px;
    max-width: 1000px;
  }
}
@media only screen and (max-width: 1399px) {
  .work-area-work-page .section-content-wrapper {
    margin-top: 44px;
    max-width: 900px;
  }
}
@media only screen and (max-width: 1199px) {
  .work-area-work-page .section-content-wrapper {
    margin-bottom: 43px;
    max-width: 750px;
  }
}
@media only screen and (max-width: 991px) {
  .work-area-work-page .section-content-wrapper {
    grid-template-columns: 1fr;
  }
}
.work-area-work-page .works-wrapper-box .container.large {
  max-width: 1850px;
}
@media (min-width: 992px) {
  .work-area-work-page .work-area-2-inner {
    padding-top: 0px !important;
  }
}

.works-wrapper-8 {
  display: grid;
  gap: 68px 20px;
  grid-template-columns: repeat(2, 1fr);
}
@media only screen and (max-width: 1919px) {
  .works-wrapper-8 {
    gap: 48px 20px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-8 {
    gap: 38px 20px;
  }
}
@media (max-width: 575px) {
  .works-wrapper-8 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.works-wrapper-8 > * .image {
  transform-origin: bottom right;
}
.works-wrapper-8 > *:nth-child(2n) .image {
  transform-origin: bottom left;
}
.works-wrapper-8 .work-box .thumb {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
}
.works-wrapper-8 .work-box .thumb:hover .t-btn {
  opacity: 1;
}
.works-wrapper-8 .work-box .thumb .image {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  transform: scale(0.9);
}
.works-wrapper-8 .work-box .thumb .image img {
  transform-origin: center;
}
.works-wrapper-8 .work-box .thumb img {
  width: 100%;
  cursor: none;
}
.works-wrapper-8 .work-box .thumb .t-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.02em;
  padding: 10px 20px;
  display: inline-block;
  background-color: white;
  color: var(--black);
  border-radius: 50px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  margin: -25px 0 0 -65px;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  pointer-events: none;
}
.works-wrapper-8 .work-box .content {
  margin-top: 14px;
}
.works-wrapper-8 .work-box .title {
  font-size: 30px;
  font-weight: 310;
  line-height: 1;
  letter-spacing: -0.08em;
}
@media only screen and (max-width: 1199px) {
  .works-wrapper-8 .work-box .title {
    font-size: 22px;
  }
}
@media only screen and (max-width: 767px) {
  .works-wrapper-8 .work-box .title {
    font-size: 20px;
  }
}
.works-wrapper-8 .work-box .meta {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 10px;
}
.works-wrapper-8 .work-box .meta span {
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  display: flex;
  align-items: center;
}
.works-wrapper-8 .work-box .meta span:not(:first-child):before {
  content: "";
  width: 10px;
  height: 1px;
  background-color: currentColor;
  display: inline-block;
  margin-inline-end: 5px;
}

/* header area style  */
.header-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.header-area__inner {
  display: flex;
  align-items: center;
  gap: 0px;
  position: relative;
  height: 100px;
}
@media only screen and (max-width: 1199px) {
  .header-area__inner {
    height: 70px;
    gap: 20px;
  }
}
.header-area__inner > *:nth-child(2) {
  margin-inline-end: auto;
}
@media (min-width: 1800px) {
  .header-area .container.large {
    max-width: 1750px;
  }
}
.header-area .sticky,
.header-area .transformed {
  background-color: #F9E6DC;
}
.header-area .header__logo {
  border: 1px solid var(--border);
  padding: 17px 30px;
  border-radius: 60px;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (max-width: 1199px) {
  .header-area .header__logo {
    padding: 0;
    border: 0;
  }
}
.header-area .header__logo img {
  max-width: 95px;
  height: auto;
}
.header-area .header__logo svg {
  max-width: 95px;
  height: auto;
  display: block;
  width: auto;
  min-height: 20px;
}
.header-area .header__nav {
  border: 1px solid var(--border);
  border-radius: 60px;
  padding: 0 13px;
}
@media only screen and (max-width: 1199px) {
  .header-area .header__nav {
    padding: 0;
    border: 0;
  }
}
.header-area .main-menu li a {
  font-size: 16px;
  font-weight: 400;
  padding: 21px 17px;
}
.header-area .main-menu li a:hover {
  color: var(--action);
}
.header-area .search-icon {
  color: var(--primary);
}
@media (max-width: 575px) {
  .header-area .header__meta {
    display: none;
  }
}
@media (max-width: 575px) {
  .header-area .header__button {
    display: none;
  }
}
.header-area .rr-btn {
  font-size: 16px;
  padding: 22px 38px;
  letter-spacing: -0.04em;
}
@media only screen and (max-width: 1199px) {
  .header-area .rr-btn {
    padding: 17px 33px;
  }
}
.header-area .header__navicon i {
  font-size: 22px;
  color: var(--primary);
}

/* header area 2 style  */
.header-area-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.header-area-2__inner {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  height: 80px;
}
@media only screen and (max-width: 1199px) {
  .header-area-2__inner {
    height: 70px;
    gap: 10px;
  }
}
.header-area-2__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
.header-area-2 .sticky,
.header-area-2 .transformed {
  background-color: #FCF7F3;
}
.dark .header-area-2 .header__logo .light-logo {
  display: none;
}
.header-area-2 .header__logo .dark-logo {
  display: none;
}
.dark .header-area-2 .header__logo .dark-logo {
  display: block;
}
.header-area-2 .header__logo img {
  max-width: 120px;
}
.header-area-2 .header__nav {
  margin-right: -50%;
}
.header-area-2 .main-menu li a {
  font-size: 16px;
  font-weight: 400;
  padding: 21px 17px;
}
.header-area-2 .main-menu li a:hover {
  color: var(--action);
}
.header-area-2 .search-icon {
  color: var(--primary);
}
@media (max-width: 575px) {
  .header-area-2 .header__meta {
    display: none;
  }
}
@media (max-width: 575px) {
  .header-area-2 .header__button {
    display: none;
  }
}
.header-area-2 .rr-btn {
  padding: 16px 28px;
  font-size: 16px;
  letter-spacing: -0.04em;
}
.header-area-2 .side-toggle {
  width: 50px;
  height: 50px;
  background-color: #F1E8E1;
  border-radius: 50%;
}
.header-area-2 .side-toggle i {
  font-size: 22px;
}

/* header area 3 style  */
.header-area-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.header-area-3__inner {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  position: relative;
  padding-top: 35px;
}
@media only screen and (max-width: 1199px) {
  .header-area-3__inner {
    padding-top: 25px;
  }
}
.header-area-3__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
@media (min-width: 1850px) {
  .header-area-3 .container.large {
    max-width: 1850px;
  }
}
.header-area-3 .header__logo img {
  max-width: 120px;
}
.header-area-3 .menu li a {
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
  padding: 0 0;
  color: var(--primary);
}
.header-area-3 .menu li a:hover {
  color: var(--secondary);
}
.header-area-3 .menu > ul {
  display: block;
}
.header-area-3 .main-menu ul:hover li {
  opacity: 0.3;
}
.header-area-3 .main-menu ul li {
  font-size: 22px;
  line-height: 30px;
  color: var(--primary);
  transition-property: opacity;
  transition-duration: 500ms;
}
.header-area-3 .main-menu ul li:hover {
  opacity: 1;
}
.header-area-3 .main-menu ul li:hover a strong {
  opacity: 1;
  top: -23px;
}
.header-area-3 .main-menu ul li a strong {
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 250ms;
}
.header-area-3 .side-toggle {
  width: 50px;
  height: 50px;
  background-color: #F1E8E1;
  border-radius: 50%;
}
.header-area-3 .side-toggle i {
  font-size: 22px;
}

/* header area 4 style  */
.header-area-4 {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
}
.header-area-4__inner {
  display: flex;
  border-top: 1px solid var(--black);
  padding-top: 20px;
  gap: 10px;
  position: relative;
  height: 80px;
}
@media only screen and (max-width: 1199px) {
  .header-area-4__inner {
    height: 70px;
    gap: 10px;
  }
}
.header-area-4__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
@media (min-width: 1650px) {
  .header-area-4 .container.large {
    max-width: 1650px;
  }
}
.header-area-4 .header__logo img {
  max-width: 120px;
}
.header-area-4 .header__middel {
  margin-right: 320px;
}
@media only screen and (max-width: 1199px) {
  .header-area-4 .header__middel {
    margin-left: 100px;
  }
}
@media only screen and (max-width: 991px) {
  .header-area-4 .header__middel {
    display: none;
  }
}
.header-area-4 .header__middel p {
  text-transform: uppercase;
  color: var(--black);
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
}
.header-area-4 .main-menu li a {
  font-size: 16px;
  font-weight: 400;
  padding: 21px 17px;
}
.header-area-4 .main-menu li a:hover {
  color: var(--action);
}
.header-area-4 .search-icon {
  color: var(--primary);
}
@media (max-width: 575px) {
  .header-area-4 .header__meta {
    display: none;
  }
}
.header-area-4 .header__navicon button {
  text-transform: uppercase;
}

/* header area 5 style  */
.header-area-5 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.header-area-5__inner {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  border-bottom: 1px solid var(--border);
}
@media only screen and (max-width: 1199px) {
  .header-area-5__inner {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
.header-area-5__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
.header-area-5 .header__logo img {
  max-width: 120px;
}
.header-area-5 .main-menu li a {
  padding: 31px 15px;
}
.header-area-5 .main-menu > ul > li:last-child > a {
  padding-right: 0;
}
.header-area-5 .menu li a {
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
  padding: 0 0;
  position: relative;
  color: var(--primary);
}
.header-area-5 .menu li a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.header-area-5 .menu li a:hover {
  color: var(--primary);
}
.header-area-5 .menu li a:hover::before {
  width: 100%;
}
.header-area-5 .menu > ul {
  display: flex;
  gap: 40px;
}
.header-area-5 .side-toggle {
  width: 40px;
  height: 40px;
  background-color: #F1E8E1;
  border-radius: 50%;
}
.header-area-5 .side-toggle i {
  font-size: 22px;
}

/* header area 6 style  */
.header-area-6 {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
}
.header-area-6__inner {
  display: flex;
  border-top: 1px solid var(--border);
  padding-top: 20px;
  gap: 10px;
  position: relative;
  height: 80px;
}
@media only screen and (max-width: 1199px) {
  .header-area-6__inner {
    height: 70px;
    gap: 10px;
  }
}
.header-area-6__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
@media (min-width: 1870px) {
  .header-area-6 .container.large {
    max-width: 1870px;
  }
}
.header-area-6 .header__logo img {
  max-width: 120px;
}
.header-area-6 .header__middel {
  margin-right: 320px;
}
@media only screen and (max-width: 1199px) {
  .header-area-6 .header__middel {
    margin-left: 100px;
  }
}
@media only screen and (max-width: 991px) {
  .header-area-6 .header__middel {
    display: none;
  }
}
.header-area-6 .header__middel p {
  text-transform: uppercase;
  color: var(--primary);
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
}
.header-area-6 .main-menu li a {
  font-size: 16px;
  font-weight: 400;
  padding: 21px 17px;
}
.header-area-6 .main-menu li a:hover {
  color: var(--action);
}
.header-area-6 .search-icon {
  color: var(--primary);
}
@media (max-width: 575px) {
  .header-area-6 .header__meta {
    display: none;
  }
}
.header-area-6 .header__navicon button {
  text-transform: uppercase;
  font-size: 14px;
  color: var(--primary);
}

/* header area 8 style  */
.header-area-7 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.header-area-7__inner {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  height: 80px;
}
@media only screen and (max-width: 1199px) {
  .header-area-7__inner {
    height: 70px;
    gap: 10px;
  }
}
.header-area-7__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
@media (min-width: 1850px) {
  .header-area-7 .container.large {
    max-width: 1850px;
  }
}
.header-area-7 .sticky,
.header-area-7 .transformed {
  background-color: #FCF7F3;
}
.header-area-7 .header__logo img {
  max-width: 95px;
}
.header-area-7 .header__nav {
  margin-right: -50%;
}
.header-area-7 .main-menu > ul > li > a {
  font-size: 14px;
  font-weight: 600;
  padding: 21px 25px;
  text-transform: uppercase;
  color: var(--white);
}
.header-area-7 .main-menu > ul > li > a:hover {
  opacity: 0.8;
}
.header-area-7 .search-icon {
  color: var(--primary);
}
@media (max-width: 575px) {
  .header-area-7 .header__meta {
    display: none;
  }
}
.header-area-7 .side-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  line-height: 16px;
  text-transform: uppercase;
  color: var(--white);
}
.header-area-7 .side-toggle i {
  font-size: 22px;
}

/* header area 8 style  */
.header-area-8 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.header-area-8__inner {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  height: 80px;
}
@media only screen and (max-width: 1199px) {
  .header-area-8__inner {
    height: 70px;
    gap: 10px;
  }
}
.header-area-8__inner > *:nth-child(1) {
  margin-inline-end: auto;
}
@media (min-width: 1850px) {
  .header-area-8 .container.large {
    max-width: 1850px;
  }
}
.header-area-8 .sticky,
.header-area-8 .transformed {
  background-color: #FCF7F3;
}
.header-area-8 .header__logo img {
  max-width: 120px;
}
.header-area-8 .header__nav {
  margin-right: -50%;
}
.header-area-8 .main-menu li a {
  font-size: 14px;
  font-weight: 600;
  padding: 21px 21px;
  text-transform: uppercase;
}
.header-area-8 .main-menu li a:hover {
  color: var(--action);
}
.header-area-8 .search-icon {
  color: var(--primary);
}
@media (max-width: 575px) {
  .header-area-8 .header__meta {
    display: none;
  }
}
.header-area-8 .side-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  line-height: 16px;
  text-transform: uppercase;
  color: var(--primary);
}
.header-area-8 .side-toggle i {
  font-size: 22px;
}

/* footer area style  */
.footer-area .footer-top-inner {
  padding-top: 92px;
  padding-bottom: 50px;
  display: grid;
  gap: 30px 60px;
  grid-template-columns: 1fr 660px;
}
@media only screen and (max-width: 1919px) {
  .footer-area .footer-top-inner {
    padding-top: 72px;
    padding-bottom: 40px;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area .footer-top-inner {
    grid-template-columns: 1fr 580px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area .footer-top-inner {
    grid-template-columns: 1fr 500px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area .footer-top-inner {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area .footer-top-inner {
    grid-template-columns: 1fr;
  }
}
.footer-area .footer-top-inner .info-text .text {
  max-width: 510px;
  font-size: 30px;
  line-height: 1.26;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .footer-area .footer-top-inner .info-text .text {
    font-size: 22px;
  }
}
.footer-area .footer-top-inner .info-link a {
  font-size: 30px;
  line-height: 1.5;
  color: var(--black-2);
  position: relative;
}
.dark .footer-area .footer-top-inner .info-link a {
  color: #555555;
}
@media only screen and (max-width: 1919px) {
  .footer-area .footer-top-inner .info-link a {
    font-size: 22px;
  }
}
.footer-area .footer-top-inner .info-link a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  transition: all 0.3s;
  background-color: currentColor;
}
.footer-area .footer-top-inner .info-link a:hover {
  color: var(--black);
}
.dark .footer-area .footer-top-inner .info-link a:hover {
  color: var(--white);
}
.footer-area .footer-top-inner .info-link a:hover::before {
  width: 0;
}
.footer-area .footer-logo {
  margin-top: 8px;
  max-width: 657px;
}
.footer-area .footer-logo svg {
  max-width: 100%;
  height: auto;
  display: block;
}
@media only screen and (max-width: 1919px) {
  .footer-area .footer-logo {
    max-width: 257px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area .footer-logo {
    max-width: 207px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area .footer-logo {
    max-width: 147px;
  }
}
.footer-area .footer-widget-wrapper-box {
  border-top: 1px solid var(--border);
  padding-top: 97px;
  padding-bottom: 94px;
}
@media only screen and (max-width: 1919px) {
  .footer-area .footer-widget-wrapper-box {
    padding-top: 77px;
    padding-bottom: 74px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area .footer-widget-wrapper-box {
    padding-top: 57px;
    padding-bottom: 54px;
  }
}
.footer-area .footer-widget-wrapper {
  display: grid;
  gap: 30px 170px;
  grid-template-columns: 1fr auto auto auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1399px) {
  .footer-area .footer-widget-wrapper {
    gap: 30px 130px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area .footer-widget-wrapper {
    gap: 30px 90px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 575px) {
  .footer-area .footer-widget-wrapper {
    grid-template-columns: 1fr;
  }
}
.footer-area .subscribe-form {
  max-width: 515px;
}
.footer-area .subscribe-form .input-field {
  display: flex;
  gap: 10px;
  background-color: rgba(17, 17, 17, 0.05);
  padding: 28px 30px;
  border-radius: 50px;
}
.dark .footer-area .subscribe-form .input-field {
  background-color: rgba(255, 255, 255, 0.05);
}
@media only screen and (max-width: 1919px) {
  .footer-area .subscribe-form .input-field {
    padding: 18px 30px;
  }
}
.footer-area .subscribe-form .input-field input {
  width: 100%;
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 22px;
  line-height: 1;
  color: var(--primary);
}
.footer-area .subscribe-form .input-field input::placeholder {
  color: rgba(17, 17, 17, 0.3);
}
.dark .footer-area .subscribe-form .input-field input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}
.footer-area .subscribe-form .input-field input:-webkit-autofill, .footer-area .subscribe-form .input-field input:-webkit-autofill:focus {
  transition: background-color 0s 600000s, color 0s 600000s !important;
}
.footer-area .subscription-text {
  margin-top: 23px;
}
.footer-area .subscription-text .text {
  font-size: 22px;
  line-height: 28px;
  color: var(--primary);
  max-width: 345px;
}
.footer-area .subscription-text .text a {
  position: relative;
}
.footer-area .subscription-text .text a::before {
  transition: all 0.5s;
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: "";
  background-color: currentColor;
}
.footer-area .subscription-text .text a:hover:hover::before {
  width: 0;
}
.footer-area .footer-widget-box .title {
  font-size: 22px;
  line-height: 20px;
  margin-bottom: 30px;
  color: var(--black-2);
  font-family: var(--font_dmsans);
  font-weight: 400;
}
.dark .footer-area .footer-widget-box .title {
  color: #555555;
}
@media only screen and (max-width: 1919px) {
  .footer-area .footer-widget-box .title {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area .footer-widget-box.newsletter {
    order: 4;
    grid-column: span 2;
  }
}
@media (max-width: 575px) {
  .footer-area .footer-widget-box.newsletter {
    grid-column: auto;
  }
}
.footer-area .footer-nav-list:hover li a {
  opacity: 0.3;
}
.footer-area .footer-nav-list li {
  font-size: 22px;
  line-height: 30px;
  color: var(--primary);
  transition-property: opacity;
  transition-duration: 500ms;
}
.footer-area .footer-nav-list li a:hover {
  opacity: 1;
}
.footer-area .footer-nav-list li a:hover a strong {
  opacity: 1;
  top: -23px;
}
.footer-area .footer-nav-list li a a strong {
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 250ms;
}
.footer-area .copyright-area-inner {
  border-top: 1px solid var(--border);
  padding: 47px 0;
}
@media only screen and (max-width: 1919px) {
  .footer-area .copyright-area-inner {
    padding: 37px 0;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area .copyright-area-inner {
    padding: 27px 0;
  }
}
.footer-area .copyright-text .text {
  font-size: 24px;
  line-height: 1;
  color: var(--primary);
  text-align: center;
}
@media only screen and (max-width: 1399px) {
  .footer-area .copyright-text .text {
    font-size: 20px;
  }
}
.footer-area .copyright-text .text a {
  color: #999999;
  transition: all 0.3s;
  position: relative;
}
.dark .footer-area .copyright-text .text a {
  color: #555555;
}
.footer-area .copyright-text .text a::before {
  width: 0%;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area .copyright-text .text a:hover {
  color: var(--black);
}
.dark .footer-area .copyright-text .text a:hover {
  color: var(--white);
}
.footer-area .copyright-text .text a:hover::before {
  width: 100%;
}

/* footer area 2 style  */
.footer-area-2 .footer-widget-wrapper-box {
  border-top: 1px solid var(--border);
  padding-top: 97px;
  padding-bottom: 94px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-2 .footer-widget-wrapper-box {
    padding-top: 77px;
    padding-bottom: 74px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .footer-widget-wrapper-box {
    padding-top: 57px;
    padding-bottom: 54px;
  }
}
.footer-area-2 .footer-widget-wrapper {
  display: grid;
  gap: 30px 170px;
  grid-template-columns: 1fr auto auto auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1399px) {
  .footer-area-2 .footer-widget-wrapper {
    gap: 30px 130px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .footer-widget-wrapper {
    gap: 30px 90px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-2 .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-2 .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 575px) {
  .footer-area-2 .footer-widget-wrapper {
    grid-template-columns: 1fr;
  }
}
.footer-area-2 .subscribe-form {
  max-width: 515px;
}
.footer-area-2 .subscribe-form .input-field {
  display: flex;
  gap: 10px;
  background-color: #F4EDE7;
  padding: 28px 30px;
  border-radius: 50px;
}
.dark .footer-area-2 .subscribe-form .input-field {
  background-color: #1D1D1D;
}
@media only screen and (max-width: 1919px) {
  .footer-area-2 .subscribe-form .input-field {
    padding: 22px 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .subscribe-form .input-field {
    padding: 18px 30px;
  }
}
.footer-area-2 .subscribe-form .input-field input {
  width: 100%;
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 22px;
  line-height: 1;
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .subscribe-form .input-field input {
    font-size: 18px;
  }
}
.footer-area-2 .subscribe-form .input-field input::placeholder {
  color: rgba(17, 17, 17, 0.3);
}
.dark .footer-area-2 .subscribe-form .input-field input::placeholder {
  color: rgba(252, 247, 243, 0.3);
}
.footer-area-2 .subscribe-form .input-field input:-webkit-autofill, .footer-area-2 .subscribe-form .input-field input:-webkit-autofill:focus {
  transition: background-color 0s 600000s, color 0s 600000s !important;
}
.footer-area-2 .subscription-text {
  margin-top: 23px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .subscription-text {
    margin-top: 18px;
  }
}
.footer-area-2 .subscription-text .text {
  font-size: 22px;
  line-height: 28px;
  color: var(--primary);
  max-width: 345px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .subscription-text .text {
    font-size: 18px;
  }
}
.footer-area-2 .subscription-text .text a {
  position: relative;
}
.footer-area-2 .subscription-text .text a::before {
  transition: all 0.5s;
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: "";
  background-color: currentColor;
}
.footer-area-2 .subscription-text .text a:hover:hover::before {
  width: 0;
}
.footer-area-2 .footer-widget-box .title {
  font-size: 22px;
  line-height: 20px;
  margin-bottom: 30px;
  color: var(--black-2);
  font-family: var(--font_dmsans);
}
.dark .footer-area-2 .footer-widget-box .title {
  color: #555555;
}
@media only screen and (max-width: 1919px) {
  .footer-area-2 .footer-widget-box .title {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-2 .footer-widget-box.newsletter {
    order: 4;
    grid-column: span 2;
  }
}
@media (max-width: 575px) {
  .footer-area-2 .footer-widget-box.newsletter {
    grid-column: auto;
  }
}
.footer-area-2 .footer-nav-list:hover li a {
  opacity: 0.3;
}
.footer-area-2 .footer-nav-list li {
  font-size: 22px;
  line-height: 30px;
  color: var(--primary);
  transition-property: opacity;
  transition-duration: 500ms;
}
.footer-area-2 .footer-nav-list li a:hover {
  opacity: 1;
}
.footer-area-2 .footer-nav-list li a:hover a strong {
  opacity: 1;
  top: -23px;
}
.footer-area-2 .footer-nav-list li a a strong {
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 250ms;
}
.footer-area-2 .copyright-area-inner {
  border-top: 1px solid var(--border);
  padding: 47px 0;
}
@media only screen and (max-width: 1919px) {
  .footer-area-2 .copyright-area-inner {
    padding: 37px 0;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area-2 .copyright-area-inner {
    padding: 27px 0;
  }
}
.footer-area-2 .copyright-text .text {
  font-size: 24px;
  line-height: 1;
  color: var(--primary);
  text-align: center;
}
@media only screen and (max-width: 1399px) {
  .footer-area-2 .copyright-text .text {
    font-size: 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-2 .copyright-text .text {
    font-size: 18px;
  }
}
.footer-area-2 .copyright-text .text a {
  color: #999999;
  transition: all 0.3s;
  position: relative;
}
.dark .footer-area-2 .copyright-text .text a {
  color: #555555;
}
.footer-area-2 .copyright-text .text a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area-2 .copyright-text .text a:hover {
  color: var(--primary);
}
.footer-area-2 .copyright-text .text a:hover::before {
  width: 100%;
}

/* footer area 3 style  */
.footer-area-3 .footer-widget-wrapper-box {
  padding-top: 97px;
  padding-bottom: 94px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-3 .footer-widget-wrapper-box {
    padding-top: 77px;
    padding-bottom: 74px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .footer-widget-wrapper-box {
    padding-top: 57px;
    padding-bottom: 54px;
  }
}
.footer-area-3 .footer-widget-wrapper {
  display: grid;
  gap: 30px 170px;
  grid-template-columns: 1fr auto auto auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1399px) {
  .footer-area-3 .footer-widget-wrapper {
    gap: 30px 130px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .footer-widget-wrapper {
    gap: 30px 90px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-3 .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-3 .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 575px) {
  .footer-area-3 .footer-widget-wrapper {
    grid-template-columns: 1fr;
  }
}
.footer-area-3 .subscribe-form {
  max-width: 515px;
}
.footer-area-3 .subscribe-form .input-field {
  display: flex;
  gap: 10px;
  background-color: rgba(17, 17, 17, 0.05);
  padding: 28px 30px;
  border-radius: 50px;
}
.dark .footer-area-3 .subscribe-form .input-field {
  background-color: rgba(255, 255, 255, 0.05);
}
@media only screen and (max-width: 1919px) {
  .footer-area-3 .subscribe-form .input-field {
    padding: 22px 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .subscribe-form .input-field {
    padding: 18px 30px;
  }
}
.footer-area-3 .subscribe-form .input-field input {
  width: 100%;
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 22px;
  line-height: 1;
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .subscribe-form .input-field input {
    font-size: 18px;
  }
}
.footer-area-3 .subscribe-form .input-field input::placeholder {
  color: rgba(17, 17, 17, 0.3);
}
.dark .footer-area-3 .subscribe-form .input-field input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}
.footer-area-3 .subscription-text {
  margin-top: 23px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .subscription-text {
    margin-top: 18px;
  }
}
.footer-area-3 .subscription-text .text {
  font-size: 22px;
  line-height: 28px;
  color: var(--primary);
  max-width: 345px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .subscription-text .text {
    font-size: 18px;
  }
}
.footer-area-3 .subscription-text .text a {
  position: relative;
}
.footer-area-3 .subscription-text .text a::before {
  transition: all 0.5s;
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: "";
  background-color: currentColor;
}
.footer-area-3 .subscription-text .text a:hover:hover::before {
  width: 0;
}
.footer-area-3 .footer-widget-box .title {
  font-size: 22px;
  line-height: 20px;
  margin-bottom: 30px;
  color: var(--black-2);
  font-family: var(--font_dmsans);
  font-weight: 400;
}
.dark .footer-area-3 .footer-widget-box .title {
  color: #555555;
}
@media only screen and (max-width: 1919px) {
  .footer-area-3 .footer-widget-box .title {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-3 .footer-widget-box.newsletter {
    order: 4;
    grid-column: span 2;
  }
}
@media (max-width: 575px) {
  .footer-area-3 .footer-widget-box.newsletter {
    grid-column: auto;
  }
}
.footer-area-3 .footer-nav-list:hover li a {
  opacity: 0.3;
}
.footer-area-3 .footer-nav-list li {
  font-size: 22px;
  line-height: 30px;
  color: var(--primary);
  transition-property: opacity;
  transition-duration: 500ms;
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .footer-nav-list li {
    font-size: 18px;
  }
}
.footer-area-3 .footer-nav-list li a:hover {
  opacity: 1;
}
.footer-area-3 .footer-nav-list li a:hover a strong {
  opacity: 1;
  top: -23px;
}
.footer-area-3 .footer-nav-list li a a strong {
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 250ms;
}
.footer-area-3 .copyright-area-inner {
  border-top: 1px solid var(--border);
  padding: 47px 0;
}
@media only screen and (max-width: 1919px) {
  .footer-area-3 .copyright-area-inner {
    padding: 37px 0;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area-3 .copyright-area-inner {
    padding: 27px 0;
  }
}
.footer-area-3 .copyright-text .text {
  font-size: 24px;
  line-height: 1;
  color: var(--primary);
  text-align: center;
}
@media only screen and (max-width: 1399px) {
  .footer-area-3 .copyright-text .text {
    font-size: 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-3 .copyright-text .text {
    font-size: 18px;
  }
}
.footer-area-3 .copyright-text .text a {
  color: #999999;
  transition: all 0.3s;
  position: relative;
}
.dark .footer-area-3 .copyright-text .text a {
  color: #555555;
}
.footer-area-3 .copyright-text .text a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area-3 .copyright-text .text a:hover {
  color: var(--primary);
}
.footer-area-3 .copyright-text .text a:hover::before {
  width: 100%;
}

/* footer area 4 style  */
.footer-area-4 .footer-widget-wrapper-box {
  border-top: 1px solid var(--border);
  padding-top: 60px;
  padding-bottom: 60px;
  margin-top: 50px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-4 .footer-widget-wrapper-box {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area-4 .footer-widget-wrapper-box {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}
.footer-area-4 .footer-widget-wrapper {
  display: grid;
  gap: 30px 170px;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1399px) {
  .footer-area-4 .footer-widget-wrapper {
    gap: 30px 60px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-4 .footer-widget-wrapper {
    grid-template-columns: 1fr;
  }
}
.footer-area-4 .footer-logo img {
  max-width: 120px;
}
.footer-area-4 .footer-nav-list {
  display: flex;
  gap: 5px 40px;
  flex-wrap: wrap;
}
@media only screen and (max-width: 1199px) {
  .footer-area-4 .footer-nav-list {
    gap: 5px 30px;
  }
}
.footer-area-4 .footer-nav-list:hover li a {
  opacity: 0.3;
}
.footer-area-4 .footer-nav-list li {
  font-size: 20px;
  line-height: 28px;
  color: var(--primary);
  transition-property: opacity;
  transition-duration: 500ms;
}
@media only screen and (max-width: 1199px) {
  .footer-area-4 .footer-nav-list li {
    font-size: 18px;
  }
}
.footer-area-4 .footer-nav-list li a:hover {
  opacity: 1;
}
.footer-area-4 .footer-nav-list li a:hover a strong {
  opacity: 1;
  top: -23px;
}
.footer-area-4 .footer-nav-list li a a strong {
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 250ms;
}
.footer-area-4 .copyright-area-inner {
  border-top: 1px solid var(--border);
  padding-top: 36px;
  padding-bottom: 55px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-4 .copyright-area-inner {
    padding-top: 31px;
    padding-bottom: 35px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-4 .copyright-area-inner {
    padding-top: 26px;
    padding-bottom: 26px;
  }
}
.footer-area-4 .copyright-text .text {
  font-size: 20px;
  line-height: 28px;
  color: var(--primary);
  text-align: center;
}
@media only screen and (max-width: 1199px) {
  .footer-area-4 .copyright-text .text {
    font-size: 18px;
  }
}
.footer-area-4 .copyright-text .text a {
  color: #999999;
  transition: all 0.3s;
  position: relative;
}
.dark .footer-area-4 .copyright-text .text a {
  color: #555555;
}
.footer-area-4 .copyright-text .text a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area-4 .copyright-text .text a:hover {
  color: var(--primary);
}
.footer-area-4 .copyright-text .text a:hover::before {
  width: 100%;
}

.footer-area-6-inner {
  padding-bottom: 40px;
  padding-top: 134px;
}
@media only screen and (max-width: 991px) {
  .footer-area-6-inner {
    padding-top: 100px;
  }
}
@media (max-width: 575px) {
  .footer-area-6-inner {
    padding-top: 50px;
  }
}
.footer-area-6 .footer-widget-wrapper {
  display: flex;
  gap: 140px;
  justify-content: space-between;
  padding-bottom: 200px;
}
@media only screen and (max-width: 1399px) {
  .footer-area-6 .footer-widget-wrapper {
    gap: 100px;
    padding-bottom: 150px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-6 .footer-widget-wrapper {
    gap: 80px;
    padding-bottom: 120px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-6 .footer-widget-wrapper {
    gap: 50px;
    padding-bottom: 60px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-6 .footer-widget-wrapper {
    gap: 30px;
    padding-bottom: 40px;
  }
}
@media (max-width: 575px) {
  .footer-area-6 .footer-widget-wrapper {
    flex-wrap: wrap;
  }
}
.footer-area-6 .footer-widget__media {
  margin-left: auto;
}
@media (max-width: 575px) {
  .footer-area-6 .footer-widget__media {
    margin-right: auto;
    margin-left: auto;
  }
}
.footer-area-6 .footer-widget__content {
  max-width: 1130px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-6 .footer-widget__content {
    max-width: 1000px;
  }
}
@media (max-width: 575px) {
  .footer-area-6 .footer-widget__content {
    max-width: 100%;
  }
}
.footer-area-6 .footer-widget__content-wrapper {
  border-top: 1px solid var(--border);
  padding-top: 30px;
  display: flex;
  gap: 240px;
}
@media only screen and (max-width: 1399px) {
  .footer-area-6 .footer-widget__content-wrapper {
    gap: 150px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-6 .footer-widget__content-wrapper {
    gap: 50px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-6 .footer-widget__content-wrapper {
    flex-wrap: wrap;
  }
}
.footer-area-6 .footer-widget__content-item span {
  font-size: 14px;
  color: var(--primary);
  line-height: 16px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 45px;
  display: inline-block;
}
@media only screen and (max-width: 991px) {
  .footer-area-6 .footer-widget__content-item span {
    margin-bottom: 20px;
  }
}
.footer-area-6 .footer-widget__content-item .description {
  max-width: 440px;
  color: var(--primary);
  font-size: 30px;
  line-height: 38px;
  letter-spacing: -0.6px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-6 .footer-widget__content-item .description {
    font-size: 25px;
    line-height: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-6 .footer-widget__content-item .description {
    max-width: 100%;
    font-size: 22px;
  }
}
.footer-area-6 .footer-widget-title {
  font-size: 100px;
  line-height: 0.95;
  letter-spacing: -5px;
  margin-bottom: 100px;
}
@media only screen and (max-width: 1199px) {
  .footer-area-6 .footer-widget-title {
    font-size: 80px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-6 .footer-widget-title {
    font-size: 50px;
    letter-spacing: 0;
    margin-bottom: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-6 .footer-widget-title {
    font-size: 35px;
  }
}
.footer-area-6 .footer-widget-nav-list {
  padding-left: 27px;
}
.footer-area-6 .footer-widget-nav-list li {
  position: relative;
}
.footer-area-6 .footer-widget-nav-list li::before {
  content: "";
  width: 7px;
  height: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 500px;
  background-color: var(--primary);
  position: absolute;
  left: 0;
  transform: translate(-27px, 15px);
  margin-right: 30px;
}
.footer-area-6 .footer-widget-nav-list li:not(:last-child) {
  margin-bottom: 5px;
}
.footer-area-6 .footer-widget-nav-list li a {
  font-size: 30px;
  line-height: 36px;
  color: var(--primary);
  text-transform: capitalize;
}
@media only screen and (max-width: 1199px) {
  .footer-area-6 .footer-widget-nav-list li a {
    font-size: 20px;
    line-height: 30px;
  }
}
.footer-area-6 .copyright-area-inner {
  border-top: 1px solid var(--border);
  padding-top: 40px;
}
.footer-area-6 .copyright-socail-list {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 10px 41px;
  position: relative;
  padding-bottom: 20px;
  flex-wrap: wrap;
}
@media (max-width: 575px) {
  .footer-area-6 .copyright-socail-list {
    gap: 10px 30px;
  }
}
.footer-area-6 .copyright-socail-list::before {
  width: 150px;
  height: 1px;
  content: "";
  position: absolute;
  bottom: 0;
  background-color: var(--border);
}
.footer-area-6 .copyright-socail-list li {
  position: relative;
}
.footer-area-6 .copyright-socail-list li:not(:last-child)::before {
  content: "";
  width: 1px;
  height: 10px;
  right: -50%;
  position: absolute;
  background-color: #999999;
  transform: translate(-10px, 3px);
}
.dark .footer-area-6 .copyright-socail-list li:not(:last-child)::before {
  background-color: #555555;
}
@media (max-width: 575px) {
  .footer-area-6 .copyright-socail-list li:not(:last-child)::before {
    transform: translate(-17px, 3px);
  }
}
.footer-area-6 .copyright-socail-list li a {
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  text-transform: uppercase;
  color: var(--primary);
  text-align: center;
  position: relative;
}
.footer-area-6 .copyright-socail-list li a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area-6 .copyright-socail-list li a:hover {
  color: #999999;
}
.footer-area-6 .copyright-socail-list li a:hover::before {
  width: 100%;
}
.footer-area-6 .copyright-text {
  padding-top: 20px;
}
.footer-area-6 .copyright-text .text {
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  text-transform: uppercase;
  color: var(--primary);
  text-align: center;
}
.footer-area-6 .copyright-text .text a {
  transition: all 0.3s;
  position: relative;
}
.footer-area-6 .copyright-text .text a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area-6 .copyright-text .text a:hover {
  color: #999999;
}
.footer-area-6 .copyright-text .text a:hover::before {
  width: 100%;
}

/* footer area inner page style  */
.footer-area-inner-page .footer-top-inner {
  padding-top: 50px;
  border-top: 1px solid var(--border);
  padding-bottom: 50px;
  margin-top: 50px;
  display: grid;
  gap: 30px 60px;
  grid-template-columns: 1fr 660px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .footer-top-inner {
    padding-top: 40px;
    padding-bottom: 40px;
    margin-top: 30px;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area-inner-page .footer-top-inner {
    grid-template-columns: 1fr 580px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .footer-top-inner {
    grid-template-columns: 1fr 470px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-inner-page .footer-top-inner {
    grid-template-columns: 1fr 430px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-inner-page .footer-top-inner {
    grid-template-columns: 1fr;
  }
}
.footer-area-inner-page .footer-top-inner .info-text .text {
  max-width: 510px;
  font-size: 30px;
  line-height: 1.26;
  color: var(--primary);
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .footer-top-inner .info-text .text {
    font-size: 22px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .footer-top-inner .info-text .text {
    font-size: 18px;
  }
}
.footer-area-inner-page .footer-top-inner .info-link a {
  font-size: 30px;
  line-height: 1.5;
  color: var(--black-2);
  position: relative;
}
.dark .footer-area-inner-page .footer-top-inner .info-link a {
  color: #555555;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .footer-top-inner .info-link a {
    font-size: 22px;
  }
}
.footer-area-inner-page .footer-top-inner .info-link a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  transition: all 0.3s;
  background-color: currentColor;
}
.footer-area-inner-page .footer-top-inner .info-link a:hover {
  color: var(--primary);
}
.footer-area-inner-page .footer-top-inner .info-link a:hover::before {
  width: 0;
}
.footer-area-inner-page .footer-logo {
  margin-top: 8px;
  max-width: 657px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .footer-logo {
    max-width: 257px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .footer-logo {
    max-width: 207px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-inner-page .footer-logo {
    max-width: 147px;
  }
}
.footer-area-inner-page .footer-widget-wrapper-box {
  border-top: 1px solid var(--border);
  padding-top: 97px;
  padding-bottom: 94px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .footer-widget-wrapper-box {
    padding-top: 77px;
    padding-bottom: 74px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .footer-widget-wrapper-box {
    padding-top: 57px;
    padding-bottom: 54px;
  }
}
.footer-area-inner-page .footer-widget-wrapper {
  display: grid;
  gap: 30px 170px;
  grid-template-columns: 1fr auto auto auto;
  justify-content: space-between;
}
@media only screen and (max-width: 1399px) {
  .footer-area-inner-page .footer-widget-wrapper {
    gap: 30px 130px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .footer-widget-wrapper {
    gap: 30px 90px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-inner-page .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area-inner-page .footer-widget-wrapper {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 575px) {
  .footer-area-inner-page .footer-widget-wrapper {
    grid-template-columns: 1fr;
  }
}
.footer-area-inner-page .subscribe-form {
  max-width: 515px;
}
.footer-area-inner-page .subscribe-form .input-field {
  display: flex;
  gap: 10px;
  background-color: rgba(17, 17, 17, 0.05);
  padding: 32px 30px;
  border-radius: 50px;
}
.dark .footer-area-inner-page .subscribe-form .input-field {
  background-color: rgba(255, 255, 255, 0.05);
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .subscribe-form .input-field {
    padding: 22px 30px;
  }
}
.footer-area-inner-page .subscribe-form .input-field input {
  width: 100%;
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 22px;
  color: var(--primary);
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .subscribe-form .input-field input {
    font-size: 18px;
  }
}
.footer-area-inner-page .subscribe-form .input-field input::placeholder {
  line-height: 1;
  color: rgba(17, 17, 17, 0.3);
}
.dark .footer-area-inner-page .subscribe-form .input-field input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}
.footer-area-inner-page .subscription-text {
  margin-top: 23px;
}
.footer-area-inner-page .subscription-text .text {
  font-size: 22px;
  line-height: 28px;
  color: var(--primary);
  max-width: 345px;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .subscription-text .text {
    font-size: 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .subscription-text .text {
    font-size: 18px;
  }
}
.footer-area-inner-page .subscription-text .text a {
  position: relative;
}
.footer-area-inner-page .subscription-text .text a::before {
  transition: all 0.5s;
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: "";
  background-color: currentColor;
}
.footer-area-inner-page .subscription-text .text a:hover:hover::before {
  width: 0;
}
.footer-area-inner-page .footer-widget-box .title {
  font-size: 22px;
  line-height: 20px;
  margin-bottom: 30px;
  color: var(--black-2);
  font-family: var(--font_dmsans);
}
.dark .footer-area-inner-page .footer-widget-box .title {
  color: #555555;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .footer-widget-box .title {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 991px) {
  .footer-area-inner-page .footer-widget-box.newsletter {
    order: 4;
    grid-column: span 2;
  }
}
@media (max-width: 575px) {
  .footer-area-inner-page .footer-widget-box.newsletter {
    grid-column: auto;
  }
}
.footer-area-inner-page .footer-nav-list:hover li a {
  opacity: 0.3;
}
.footer-area-inner-page .footer-nav-list li {
  font-size: 22px;
  line-height: 30px;
  color: var(--primary);
  transition-property: opacity;
  transition-duration: 500ms;
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .footer-nav-list li {
    font-size: 18px;
  }
}
.footer-area-inner-page .footer-nav-list li a:hover {
  opacity: 1;
}
.footer-area-inner-page .footer-nav-list li a:hover a strong {
  opacity: 1;
  top: -23px;
}
.footer-area-inner-page .footer-nav-list li a a strong {
  opacity: 0;
  transition-property: opacity, top;
  transition-duration: 250ms;
}
.footer-area-inner-page .copyright-area-inner {
  border-top: 1px solid var(--border);
  padding: 47px 0;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .copyright-area-inner {
    padding: 37px 0;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area-inner-page .copyright-area-inner {
    padding: 27px 0;
  }
}
.footer-area-inner-page .copyright-text .text {
  font-size: 24px;
  line-height: 1;
  color: var(--primary);
  text-align: center;
}
@media only screen and (max-width: 1919px) {
  .footer-area-inner-page .copyright-text .text {
    font-size: 22px;
  }
}
@media only screen and (max-width: 1399px) {
  .footer-area-inner-page .copyright-text .text {
    font-size: 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .footer-area-inner-page .copyright-text .text {
    font-size: 18px;
  }
}
.footer-area-inner-page .copyright-text .text a {
  color: #999999;
  transition: all 0.3s;
  position: relative;
}
.dark .footer-area-inner-page .copyright-text .text a {
  color: #555555;
}
.footer-area-inner-page .copyright-text .text a::before {
  width: 0;
  height: 1px;
  background-color: currentColor;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s;
}
.footer-area-inner-page .copyright-text .text a:hover {
  color: var(--primary);
}
.footer-area-inner-page .copyright-text .text a:hover::before {
  width: 100%;
}/*# sourceMappingURL=style.css.map */